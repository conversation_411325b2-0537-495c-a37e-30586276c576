# Al-Hassan Stone Factory Management System - User Manual

## Table of Contents
1. [System Overview](#system-overview)
2. [Getting Started](#getting-started)
3. [User Roles and Permissions](#user-roles-and-permissions)
4. [Customer Management](#customer-management)
5. [Supplier Management](#supplier-management)
6. [Inventory Management](#inventory-management)
7. [Sales and Invoicing](#sales-and-invoicing)
8. [Purchase Orders](#purchase-orders)
9. [Financial Management](#financial-management)
10. [Reporting System](#reporting-system)
11. [Python Analytics](#python-analytics)
12. [Troubleshooting](#troubleshooting)

## System Overview

The Al-Hassan Stone Factory Management System is a comprehensive business management solution built on FileMaker Pro with Python integration. It manages all aspects of a stone/granite manufacturing business including:

- Customer and supplier relationships
- Inventory tracking (blocks and cuts)
- Sales and purchase order processing
- Financial management and reporting
- Advanced analytics and automation

### Key Features
- **Multi-currency support** (EGP, USD, EUR, SAR)
- **Role-based access control** (Ad<PERSON>, Manager, Accountant, Operator)
- **Automated alerts and notifications**
- **Comprehensive reporting system**
- **Python-powered analytics**
- **Real-time inventory tracking**

## Getting Started

### System Requirements
- FileMaker Pro 19 or later
- Windows 10/11 or macOS 10.15+
- Python 3.8+ (for analytics features)
- Minimum 8GB RAM
- 10GB available disk space

### First-Time Setup

1. **Install FileMaker Pro**
   - Install FileMaker Pro on your computer
   - Ensure you have appropriate licensing

2. **Open the Database**
   - Open `AlHassanStone.fmp12`
   - The system will prompt for login credentials

3. **Initial Login**
   - Default admin username: `admin`
   - Default password: `AlHassan2024`
   - **Important**: Change the default password immediately

4. **Configure Python Integration** (Optional)
   - Install Python dependencies: `pip install -r requirements.txt`
   - Configure database connection in `python_integration/database_connector.py`
   - Set up email notifications in `automation_scripts.py`

### Navigation Basics

The system uses a dashboard-based navigation:
- **Main Dashboard**: Central hub with quick access to all modules
- **Breadcrumb Navigation**: Shows your current location
- **Quick Search**: Global search functionality
- **User Menu**: Access to profile settings and logout

## User Roles and Permissions

### Admin
- **Full system access**
- User management
- System configuration
- All reports and analytics
- Database maintenance

### Manager
- **Business operations management**
- Customer and supplier management
- Inventory oversight
- Financial reporting
- Cannot modify user accounts

### Accountant
- **Financial data focus**
- Customer and supplier financial records
- Invoice and payment management
- Financial reports
- Limited inventory access (view only)

### Operator
- **Daily operations**
- Customer data entry
- Invoice creation
- Inventory management
- Basic reporting

## Customer Management

### Adding a New Customer

1. Navigate to **Customer Management** → **New Customer**
2. Fill in required fields:
   - **Full Name** (required)
   - **Phone Number** (required)
   - **Address**
   - **Email** (optional but recommended)
   - **Company/Contractor Name**
   - **Preferred Payment Method**
   - **Currency**
3. Click **Save**

### Customer Features

#### Customer Profile
- Complete contact information
- Payment preferences and terms
- Transaction history
- Outstanding balance tracking

#### Customer Search
- Search by name, phone, or company
- Filter by payment status
- Sort by various criteria

#### Customer Reports
- Account statements
- Sales history
- Payment history
- Outstanding balances

### Managing Customer Payments

1. Select customer from list
2. Click **Record Payment**
3. Enter payment details:
   - Amount
   - Payment method
   - Reference number
   - Date
4. Link to specific invoices if needed
5. Save payment

## Supplier Management

### Adding a New Supplier

1. Navigate to **Supplier Management** → **New Supplier**
2. Complete supplier information:
   - **Supplier Name** (required)
   - **Supplier Type** (Raw Granite/Transportation/etc.)
   - **Contact Information**
   - **Payment Terms**
   - **Products/Services**
3. Save supplier record

### Supplier Features

#### Supplier Types
- **Raw Granite**: Stone and granite suppliers
- **Transportation**: Shipping and logistics
- **Machinery**: Equipment suppliers
- **Maintenance**: Service providers
- **Other**: Miscellaneous suppliers

#### Purchase Order Management
- Create purchase orders
- Track delivery status
- Receive items
- Manage payments

#### Supplier Performance
- Delivery tracking
- Quality metrics
- Cost analysis
- Performance scoring

## Inventory Management

### Block Management

#### Adding New Blocks
1. Navigate to **Inventory** → **Blocks** → **New Block**
2. Enter block details:
   - **Block Type** (Granite, Marble, etc.)
   - **Color**
   - **Dimensions** (Length × Width × Height)
   - **Supplier**
   - **Cost Information**
   - **Location**
3. System automatically calculates volume

#### Block Status Tracking
- **Available**: Ready for cutting
- **In Use**: Currently being processed
- **Depleted**: Fully utilized
- **Reserved**: Set aside for specific orders

### Cut Management

#### Creating Cuts
1. Select source block
2. Navigate to **Create Cut**
3. Specify cut details:
   - **Customer** (if pre-ordered)
   - **Cut Type** (Slab, Tile, Custom)
   - **Dimensions**
   - **Specifications**
4. System calculates area and updates block utilization

#### Cut Tracking
- Cut status (Planned, In Progress, Completed, Delivered)
- Quality specifications
- Cost tracking
- Customer linking

### Inventory Reports
- Block utilization rates
- Slow-moving inventory
- Inventory valuation
- Reorder recommendations

## Sales and Invoicing

### Creating Invoices

1. Navigate to **Sales** → **New Invoice**
2. Select customer
3. Add invoice items:
   - Product description
   - Quantity and unit
   - Unit price
   - Link to cuts (if applicable)
4. Apply discounts or taxes
5. Set payment terms
6. Save and send invoice

### Invoice Management

#### Invoice Status
- **Draft**: Being prepared
- **Sent**: Delivered to customer
- **Paid**: Fully paid
- **Partial Payment**: Partially paid
- **Overdue**: Past due date
- **Cancelled**: Voided

#### Payment Tracking
- Link payments to invoices
- Track partial payments
- Monitor outstanding balances
- Generate payment reminders

## Purchase Orders

### Creating Purchase Orders

1. Navigate to **Purchasing** → **New Purchase Order**
2. Select supplier
3. Add items:
   - Item description
   - Quantity and specifications
   - Unit price
4. Set delivery expectations
5. Send to supplier

### Receiving Items

1. Open purchase order
2. Click **Receive Items**
3. Verify quantities and quality
4. Update block inventory (for granite orders)
5. Mark as received

## Financial Management

### Payment Processing

#### Customer Payments
- Record cash, check, or bank transfer payments
- Apply to specific invoices
- Generate receipts
- Update customer balances

#### Supplier Payments
- Process payments to suppliers
- Link to purchase orders
- Track payment methods
- Maintain payment history

### Financial Controls
- Multi-currency support
- Exchange rate management
- Payment approval workflows
- Audit trails

## Reporting System

### Customer Reports

#### Account Statements
- Detailed transaction history
- Opening and closing balances
- Payment summaries
- Customizable date ranges

#### Sales Analysis
- Customer ranking by sales volume
- Sales trends and patterns
- Profitability analysis
- Geographic analysis

#### Overdue Reports
- Customers with overdue payments
- Aging analysis
- Collection priorities
- Contact information for follow-up

### Supplier Reports

#### Performance Analysis
- Delivery performance
- Quality metrics
- Cost comparisons
- Reliability scoring

#### Cost Analysis
- Spending by supplier
- Price trend analysis
- Cost saving opportunities
- Budget variance reports

### Inventory Reports

#### Utilization Reports
- Block usage efficiency
- Waste analysis
- Optimization recommendations
- Location utilization

#### Valuation Reports
- Current inventory value
- Cost basis analysis
- Depreciation tracking
- Insurance valuations

### Financial Reports

#### Profit & Loss
- Revenue analysis
- Cost of goods sold
- Operating expenses
- Net profit calculations

#### Cash Flow
- Receivables aging
- Payables scheduling
- Cash position analysis
- Liquidity projections

## Python Analytics

### Automated Analytics

The system includes Python-powered analytics that provide:

#### Customer Intelligence
- Purchase behavior prediction
- Customer lifetime value
- Churn risk analysis
- Personalized recommendations

#### Supplier Optimization
- Performance benchmarking
- Cost optimization opportunities
- Risk assessment
- Alternative supplier suggestions

#### Inventory Intelligence
- Demand forecasting
- Optimal stock levels
- Reorder point calculations
- Seasonal trend analysis

### Automated Alerts

#### Daily Alerts
- Overdue payments
- Low inventory warnings
- Quality issues
- System anomalies

#### Weekly Reports
- Business performance summary
- Key metrics dashboard
- Trend analysis
- Action recommendations

#### Monthly Analytics
- Comprehensive business review
- Strategic insights
- Performance benchmarks
- Growth opportunities

### Setting Up Python Analytics

1. **Install Dependencies**
   ```bash
   pip install pandas numpy matplotlib seaborn scikit-learn
   pip install pyodbc schedule smtplib
   ```

2. **Configure Database Connection**
   - Edit `database_connector.py`
   - Set FileMaker Server details
   - Configure authentication

3. **Set Up Email Notifications**
   - Configure SMTP settings
   - Set recipient lists
   - Customize alert templates

4. **Schedule Automation**
   - Run `automation_scripts.py`
   - Configure schedule preferences
   - Monitor automation logs

## Troubleshooting

### Common Issues

#### Login Problems
- **Forgot Password**: Contact administrator for reset
- **Account Locked**: Wait 15 minutes or contact admin
- **Permission Denied**: Check user role assignments

#### Performance Issues
- **Slow Loading**: Check network connection
- **Memory Errors**: Close unnecessary applications
- **Database Corruption**: Contact IT support immediately

#### Data Issues
- **Missing Records**: Check user permissions
- **Calculation Errors**: Verify field relationships
- **Export Problems**: Check file permissions

### Error Messages

#### "Record is locked"
- Another user is editing the record
- Wait for them to finish or contact them
- Check for system conflicts

#### "Insufficient privileges"
- User role doesn't allow this action
- Contact administrator for permission changes
- Verify login credentials

#### "Network connection lost"
- Check internet/network connectivity
- Restart FileMaker Pro
- Contact IT support if persistent

### Getting Help

#### Internal Support
1. Check this user manual
2. Contact your system administrator
3. Review training materials
4. Ask experienced colleagues

#### Technical Support
- **Email**: <EMAIL>
- **Phone**: +20-XXX-XXXX
- **Hours**: Sunday-Thursday, 9 AM - 5 PM

#### Training Resources
- Video tutorials available in system
- Monthly training sessions
- Online documentation
- User community forum

### Best Practices

#### Data Entry
- Always fill required fields
- Use consistent naming conventions
- Verify data before saving
- Regular data backups

#### Security
- Change passwords regularly
- Log out when finished
- Don't share login credentials
- Report suspicious activity

#### Performance
- Close unused layouts
- Limit large data exports
- Regular system maintenance
- Monitor disk space

## System Maintenance

### Daily Tasks
- Backup database
- Monitor system alerts
- Review error logs
- Check user activity

### Weekly Tasks
- Generate performance reports
- Review user permissions
- Update system documentation
- Clean temporary files

### Monthly Tasks
- Full system backup
- Security audit
- Performance optimization
- User training updates

### Annual Tasks
- License renewal
- System upgrade planning
- Comprehensive security review
- Disaster recovery testing

---

**Document Version**: 1.0  
**Last Updated**: January 2024  
**Next Review**: July 2024

For technical support or questions about this manual, contact the Al-Hassan Stone Factory IT Department.
