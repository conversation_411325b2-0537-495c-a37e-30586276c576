# Al-Hassan Stone Factory Management System
# Python Dependencies

# Core Data Processing
pandas>=1.5.0
numpy>=1.21.0

# Database Connectivity
pyodbc>=4.0.34

# Data Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Excel File Handling
openpyxl>=3.0.9
xlsxwriter>=3.0.3

# Task Scheduling
schedule>=1.2.0

# Email Functionality
secure-smtplib>=0.1.1

# Date/Time Handling
python-dateutil>=2.8.2

# JSON Processing
jsonschema>=4.0.0

# Logging and Configuration
python-dotenv>=0.19.0

# Statistical Analysis
scipy>=1.8.0
scikit-learn>=1.1.0

# Web Scraping (if needed for external data)
requests>=2.28.0
beautifulsoup4>=4.11.0

# Data Validation
cerberus>=1.3.4

# Cryptography (for secure connections)
cryptography>=37.0.0

# Progress Bars
tqdm>=4.64.0

# Configuration Management
configparser>=5.2.0

# File System Operations
pathlib>=1.0.1

# Memory Profiling (development)
memory-profiler>=0.60.0

# Testing Framework
pytest>=7.1.0
pytest-cov>=3.0.0

# Code Quality
flake8>=4.0.0
black>=22.0.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0
