# Al-Hassan Stone Factory - FileMaker Pro Database Structure
# This file contains the DDL-equivalent structure for FileMaker Pro implementation

## Database: AlHassanStone.fmp12

### Table Definitions

## CUSTOMERS Table
CREATE TABLE CUSTOMERS (
    customer_id TEXT PRIMARY KEY DEFAULT "CUST" & SerialIncrement(CustomerSerial, 1),
    full_name TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    address TEXT,
    email TEXT,
    company_contractor_name TEXT,
    preferred_payment_method TEXT DEFAULT "Cash",
    currency TEXT DEFAULT "EGP",
    notes TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    modified_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    active_status NUMBER DEFAULT 1
);

## SUPPLIERS Table
CREATE TABLE SUPPLIERS (
    supplier_id TEXT PRIMARY KEY DEFAULT "SUPP" & SerialIncrement(SupplierSerial, 1),
    supplier_name TEXT NOT NULL,
    supplier_type TEXT NOT NULL,
    contact_person TEXT,
    phone_number TEXT NOT NULL,
    email TEXT,
    address TEXT,
    payment_terms TEXT,
    products_services TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    modified_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    active_status NUMBER DEFAULT 1
);

## INVOICES Table
CREATE TABLE INVOICES (
    invoice_id TEXT PRIMARY KEY DEFAULT "INV" & SerialIncrement(InvoiceSerial, 1),
    customer_id TEXT FOREIGN KEY REFERENCES CUSTOMERS(customer_id),
    invoice_number TEXT NOT NULL UNIQUE,
    invoice_date DATE NOT NULL DEFAULT Get(CurrentDate),
    total_amount NUMBER DEFAULT 0,
    tax_amount NUMBER DEFAULT 0,
    discount_amount NUMBER DEFAULT 0,
    net_amount NUMBER CALCULATED (total_amount - discount_amount + tax_amount),
    currency TEXT DEFAULT "EGP",
    status TEXT DEFAULT "Draft",
    due_date DATE,
    notes TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    modified_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## INVOICE_ITEMS Table
CREATE TABLE INVOICE_ITEMS (
    item_id TEXT PRIMARY KEY DEFAULT "ITEM" & SerialIncrement(ItemSerial, 1),
    invoice_id TEXT FOREIGN KEY REFERENCES INVOICES(invoice_id),
    product_description TEXT NOT NULL,
    quantity NUMBER NOT NULL DEFAULT 1,
    unit TEXT DEFAULT "sqm",
    unit_price NUMBER NOT NULL DEFAULT 0,
    total_price NUMBER CALCULATED (quantity * unit_price),
    cut_id TEXT FOREIGN KEY REFERENCES CUTS(cut_id),
    notes TEXT
);

## PAYMENTS Table
CREATE TABLE PAYMENTS (
    payment_id TEXT PRIMARY KEY DEFAULT "PAY" & SerialIncrement(PaymentSerial, 1),
    customer_id TEXT FOREIGN KEY REFERENCES CUSTOMERS(customer_id),
    invoice_id TEXT FOREIGN KEY REFERENCES INVOICES(invoice_id),
    amount NUMBER NOT NULL DEFAULT 0,
    payment_method TEXT DEFAULT "Cash",
    currency TEXT DEFAULT "EGP",
    payment_date DATE NOT NULL DEFAULT Get(CurrentDate),
    reference_number TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    modified_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## PURCHASE_ORDERS Table
CREATE TABLE PURCHASE_ORDERS (
    po_id TEXT PRIMARY KEY DEFAULT "PO" & SerialIncrement(POSerial, 1),
    supplier_id TEXT FOREIGN KEY REFERENCES SUPPLIERS(supplier_id),
    po_number TEXT NOT NULL UNIQUE,
    order_date DATE NOT NULL DEFAULT Get(CurrentDate),
    total_amount NUMBER DEFAULT 0,
    currency TEXT DEFAULT "EGP",
    status TEXT DEFAULT "Pending",
    expected_delivery DATE,
    notes TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    modified_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## PURCHASE_ORDER_ITEMS Table
CREATE TABLE PURCHASE_ORDER_ITEMS (
    po_item_id TEXT PRIMARY KEY DEFAULT "POITEM" & SerialIncrement(POItemSerial, 1),
    po_id TEXT FOREIGN KEY REFERENCES PURCHASE_ORDERS(po_id),
    item_description TEXT NOT NULL,
    quantity NUMBER NOT NULL DEFAULT 1,
    unit TEXT DEFAULT "cubic meter",
    unit_price NUMBER NOT NULL DEFAULT 0,
    total_price NUMBER CALCULATED (quantity * unit_price),
    specifications TEXT
);

## SUPPLIER_PAYMENTS Table
CREATE TABLE SUPPLIER_PAYMENTS (
    supplier_payment_id TEXT PRIMARY KEY DEFAULT "SUPPAY" & SerialIncrement(SupplierPaymentSerial, 1),
    supplier_id TEXT FOREIGN KEY REFERENCES SUPPLIERS(supplier_id),
    po_id TEXT FOREIGN KEY REFERENCES PURCHASE_ORDERS(po_id),
    amount NUMBER NOT NULL DEFAULT 0,
    payment_method TEXT DEFAULT "Bank Transfer",
    currency TEXT DEFAULT "EGP",
    payment_date DATE NOT NULL DEFAULT Get(CurrentDate),
    reference_number TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## BLOCKS Table
CREATE TABLE BLOCKS (
    block_id TEXT PRIMARY KEY DEFAULT "BLK" & SerialIncrement(BlockSerial, 1),
    supplier_id TEXT FOREIGN KEY REFERENCES SUPPLIERS(supplier_id),
    po_id TEXT FOREIGN KEY REFERENCES PURCHASE_ORDERS(po_id),
    block_type TEXT NOT NULL,
    color TEXT,
    origin TEXT,
    length NUMBER NOT NULL DEFAULT 0,
    width NUMBER NOT NULL DEFAULT 0,
    height NUMBER NOT NULL DEFAULT 0,
    volume NUMBER CALCULATED (length * width * height),
    cost_per_unit NUMBER DEFAULT 0,
    total_cost NUMBER CALCULATED (volume * cost_per_unit),
    currency TEXT DEFAULT "EGP",
    received_date DATE DEFAULT Get(CurrentDate),
    location TEXT,
    status TEXT DEFAULT "Available",
    notes TEXT
);

## CUTS Table
CREATE TABLE CUTS (
    cut_id TEXT PRIMARY KEY DEFAULT "CUT" & SerialIncrement(CutSerial, 1),
    block_id TEXT FOREIGN KEY REFERENCES BLOCKS(block_id),
    customer_id TEXT FOREIGN KEY REFERENCES CUSTOMERS(customer_id),
    cut_type TEXT DEFAULT "Slab",
    length NUMBER NOT NULL DEFAULT 0,
    width NUMBER NOT NULL DEFAULT 0,
    thickness NUMBER NOT NULL DEFAULT 0,
    area NUMBER CALCULATED (length * width),
    waste_percentage NUMBER DEFAULT 10,
    cost_per_sqm NUMBER DEFAULT 0,
    total_cost NUMBER CALCULATED (area * cost_per_sqm),
    currency TEXT DEFAULT "EGP",
    cut_date DATE DEFAULT Get(CurrentDate),
    status TEXT DEFAULT "Planned",
    specifications TEXT,
    notes TEXT
);

## EXPENSES Table
CREATE TABLE EXPENSES (
    expense_id TEXT PRIMARY KEY DEFAULT "EXP" & SerialIncrement(ExpenseSerial, 1),
    supplier_id TEXT FOREIGN KEY REFERENCES SUPPLIERS(supplier_id),
    expense_category TEXT NOT NULL,
    description TEXT NOT NULL,
    amount NUMBER NOT NULL DEFAULT 0,
    currency TEXT DEFAULT "EGP",
    expense_date DATE NOT NULL DEFAULT Get(CurrentDate),
    payment_method TEXT DEFAULT "Cash",
    reference_number TEXT,
    notes TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## USERS Table
CREATE TABLE USERS (
    user_id TEXT PRIMARY KEY DEFAULT "USER" & SerialIncrement(UserSerial, 1),
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    role TEXT NOT NULL DEFAULT "Operator",
    active_status NUMBER DEFAULT 1,
    last_login TIMESTAMP,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    modified_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## USER_SESSIONS Table
CREATE TABLE USER_SESSIONS (
    session_id TEXT PRIMARY KEY DEFAULT "SESS" & SerialIncrement(SessionSerial, 1),
    user_id TEXT FOREIGN KEY REFERENCES USERS(user_id),
    login_time TIMESTAMP DEFAULT Get(CurrentTimeStamp),
    logout_time TIMESTAMP,
    ip_address TEXT,
    active NUMBER DEFAULT 1
);

## CUSTOMER_REPORTS Table
CREATE TABLE CUSTOMER_REPORTS (
    report_id TEXT PRIMARY KEY DEFAULT "CUSTREP" & SerialIncrement(CustomerReportSerial, 1),
    customer_id TEXT FOREIGN KEY REFERENCES CUSTOMERS(customer_id),
    report_type TEXT NOT NULL,
    report_date DATE DEFAULT Get(CurrentDate),
    total_sales NUMBER DEFAULT 0,
    total_payments NUMBER DEFAULT 0,
    outstanding_balance NUMBER CALCULATED (total_sales - total_payments),
    report_data TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

## SUPPLIER_REPORTS Table
CREATE TABLE SUPPLIER_REPORTS (
    report_id TEXT PRIMARY KEY DEFAULT "SUPREP" & SerialIncrement(SupplierReportSerial, 1),
    supplier_id TEXT FOREIGN KEY REFERENCES SUPPLIERS(supplier_id),
    report_type TEXT NOT NULL,
    report_date DATE DEFAULT Get(CurrentDate),
    total_purchases NUMBER DEFAULT 0,
    total_payments NUMBER DEFAULT 0,
    outstanding_balance NUMBER CALCULATED (total_purchases - total_payments),
    report_data TEXT,
    created_date TIMESTAMP DEFAULT Get(CurrentTimeStamp)
);

### Value Lists for FileMaker Pro

## Payment Methods
PaymentMethods:
- Cash
- Bank Transfer
- Check
- Credit Card
- Installments

## Currencies
Currencies:
- EGP
- USD
- EUR
- SAR

## Supplier Types
SupplierTypes:
- Raw Granite
- Transportation
- Machinery
- Maintenance
- Tools & Equipment
- Services
- Other

## Invoice Status
InvoiceStatus:
- Draft
- Sent
- Paid
- Partial Payment
- Overdue
- Cancelled

## Purchase Order Status
POStatus:
- Pending
- Confirmed
- Received
- Cancelled

## Block Status
BlockStatus:
- Available
- In Use
- Depleted
- Reserved

## Cut Status
CutStatus:
- Planned
- In Progress
- Completed
- Delivered
- Cancelled

## Cut Types
CutTypes:
- Slab
- Tile
- Custom
- Countertop
- Flooring

## User Roles
UserRoles:
- Admin
- Manager
- Accountant
- Operator

## Expense Categories
ExpenseCategories:
- Transportation
- Maintenance
- Utilities
- Tools & Equipment
- Labor
- Marketing
- Administrative
- Other

### Relationships Definition

## Primary Relationships
CUSTOMERS::customer_id = INVOICES::customer_id
CUSTOMERS::customer_id = PAYMENTS::customer_id
CUSTOMERS::customer_id = CUTS::customer_id

SUPPLIERS::supplier_id = PURCHASE_ORDERS::supplier_id
SUPPLIERS::supplier_id = BLOCKS::supplier_id
SUPPLIERS::supplier_id = SUPPLIER_PAYMENTS::supplier_id
SUPPLIERS::supplier_id = EXPENSES::supplier_id

INVOICES::invoice_id = INVOICE_ITEMS::invoice_id
INVOICES::invoice_id = PAYMENTS::invoice_id

PURCHASE_ORDERS::po_id = PURCHASE_ORDER_ITEMS::po_id
PURCHASE_ORDERS::po_id = BLOCKS::po_id
PURCHASE_ORDERS::po_id = SUPPLIER_PAYMENTS::po_id

BLOCKS::block_id = CUTS::block_id
CUTS::cut_id = INVOICE_ITEMS::cut_id

USERS::user_id = USER_SESSIONS::user_id

CUSTOMERS::customer_id = CUSTOMER_REPORTS::customer_id
SUPPLIERS::supplier_id = SUPPLIER_REPORTS::supplier_id

### Auto-Enter Calculations

## Customer Outstanding Balance
Customer_Outstanding_Balance = 
Sum(INVOICES::net_amount) - Sum(PAYMENTS::amount)

## Supplier Outstanding Balance
Supplier_Outstanding_Balance = 
Sum(PURCHASE_ORDERS::total_amount) - Sum(SUPPLIER_PAYMENTS::amount)

## Block Remaining Volume
Block_Remaining_Volume = 
BLOCKS::volume - Sum(CUTS::area * CUTS::thickness)

## Customer Total Sales
Customer_Total_Sales = Sum(INVOICES::net_amount)

## Supplier Total Purchases
Supplier_Total_Purchases = Sum(PURCHASE_ORDERS::total_amount)

### Validation Rules

## Customer Validation
- full_name: Not Empty
- phone_number: Not Empty, Unique
- email: Valid Email Format (if not empty)
- preferred_payment_method: Member of PaymentMethods value list
- currency: Member of Currencies value list

## Supplier Validation
- supplier_name: Not Empty
- supplier_type: Member of SupplierTypes value list
- phone_number: Not Empty

## Invoice Validation
- customer_id: Must exist in CUSTOMERS table
- invoice_date: Not in future
- total_amount: >= 0
- net_amount: >= 0

## Payment Validation
- customer_id: Must exist in CUSTOMERS table
- amount: > 0
- payment_date: Not in future

## Block Validation
- length, width, height: > 0
- volume: Auto-calculated, > 0
- cost_per_unit: >= 0

## Cut Validation
- block_id: Must exist in BLOCKS table
- customer_id: Must exist in CUSTOMERS table
- length, width, thickness: > 0
- area: Auto-calculated, > 0
