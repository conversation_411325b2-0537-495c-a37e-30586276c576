# Al-Hassan Stone Factory - User Access Control System
# FileMaker Pro Security and Role-Based Access Control

## Privilege Sets Definition

### Admin Privilege Set
**Name**: [Admin]
**Description**: Full system access for administrators

**Data Access and Design**:
- Records: Create, Edit, Delete in All Tables
- Layouts: Modifiable
- Value Lists: Modifiable
- Scripts: Modifiable
- Extended Privileges: All enabled

**Available Menu Commands**: All
**Other Privileges**:
- Allow printing: Yes
- Allow exporting: Yes
- Allow user to override data validation warnings: Yes
- Allow user to disconnect when idle: No
- Idle time: 60 minutes

### Manager Privilege Set
**Name**: [Manager]
**Description**: Management level access with reporting capabilities

**Data Access and Design**:
- Records: Create, Edit, Delete (except USERS table - View Only)
- Layouts: View Only
- Value Lists: View Only
- Scripts: Executable Only
- Extended Privileges: fmapp, fmxml, fmxslt

**Available Menu Commands**: 
- All except: Define Database, Define Accounts & Privileges, Install Plug-In

**Other Privileges**:
- Allow printing: Yes
- Allow exporting: Yes
- Allow user to override data validation warnings: Yes
- Allow user to disconnect when idle: Yes
- Idle time: 120 minutes

### Accountant Privilege Set
**Name**: [Accountant]
**Description**: Financial data access with limited operational access

**Data Access and Design**:
- CUSTOMERS: Create, Edit, Delete
- SUPPLIERS: Create, Edit, Delete
- INVOICES: Create, Edit, Delete
- INVOICE_ITEMS: Create, Edit, Delete
- PAYMENTS: Create, Edit, Delete
- PURCHASE_ORDERS: Create, Edit, Delete
- PURCHASE_ORDER_ITEMS: Create, Edit, Delete
- SUPPLIER_PAYMENTS: Create, Edit, Delete
- EXPENSES: Create, Edit, Delete
- BLOCKS: View Only
- CUTS: View Only
- USERS: No Access
- USER_SESSIONS: No Access
- CUSTOMER_REPORTS: Create, Edit, Delete
- SUPPLIER_REPORTS: Create, Edit, Delete

**Available Menu Commands**: 
- File, Edit, View, Insert, Format, Records, Requests, Window, Help
- Exclude: Scripts, Tools

**Other Privileges**:
- Allow printing: Yes
- Allow exporting: Yes (Financial data only)
- Allow user to override data validation warnings: No
- Allow user to disconnect when idle: Yes
- Idle time: 90 minutes

### Operator Privilege Set
**Name**: [Operator]
**Description**: Basic operational access for daily tasks

**Data Access and Design**:
- CUSTOMERS: Create, Edit (No Delete)
- SUPPLIERS: View Only
- INVOICES: Create, Edit (No Delete)
- INVOICE_ITEMS: Create, Edit, Delete
- PAYMENTS: Create, Edit (No Delete)
- PURCHASE_ORDERS: View Only
- PURCHASE_ORDER_ITEMS: View Only
- SUPPLIER_PAYMENTS: No Access
- BLOCKS: Create, Edit (No Delete)
- CUTS: Create, Edit, Delete
- EXPENSES: No Access
- USERS: No Access
- USER_SESSIONS: No Access
- CUSTOMER_REPORTS: View Only
- SUPPLIER_REPORTS: No Access

**Available Menu Commands**: 
- File (limited), Edit, View, Insert, Records, Requests, Window, Help

**Other Privileges**:
- Allow printing: Yes (Limited to customer documents)
- Allow exporting: No
- Allow user to override data validation warnings: No
- Allow user to disconnect when idle: Yes
- Idle time: 60 minutes

## Account Management Scripts

### Script: Create New User Account
/**
 * Purpose: Create a new user account with proper validation
 */
# Set error capture on
Set Error Capture [On]

# Check if current user has admin privileges
If [Get(PrivilegeSetName) ≠ "[Admin]"]
    Show Custom Dialog ["Access Denied"; "Only administrators can create user accounts."]
    Exit Script [Text Result: "Access Denied"]
End If

# Go to user management layout
Go to Layout ["User_Management"]

# Create new user record
New Record/Request

# Show user creation dialog
Show Custom Dialog ["Create New User"; "Enter user details:";
    "Username:"; USERS::username;
    "Full Name:"; USERS::full_name;
    "Email:"; USERS::email;
    "Role:"; USERS::role]

If [Get(LastMessageChoice) = 2]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Cancelled"]
End If

# Validate required fields
If [IsEmpty(USERS::username) or IsEmpty(USERS::full_name) or IsEmpty(USERS::role)]
    Show Custom Dialog ["Error"; "Username, full name, and role are required."]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Error"]
End If

# Check if username already exists
Set Variable [$existingUser; Value: ExecuteSQL(
    "SELECT COUNT(*) FROM USERS WHERE username = ?"; 
    ""; ""; USERS::username)]

If [$existingUser > 0]
    Show Custom Dialog ["Error"; "Username already exists. Please choose a different username."]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Error"]
End If

# Generate temporary password
Set Variable [$tempPassword; Value: "AlHassan" & Right(Get(CurrentTimeStamp); 4)]

# Set user fields
Set Field [USERS::user_id; "USER" & SerialIncrement(UserSerial, 1)]
Set Field [USERS::password_hash; Hash($tempPassword; "SHA256")]
Set Field [USERS::active_status; 1]
Set Field [USERS::created_date; Get(CurrentTimeStamp)]
Set Field [USERS::modified_date; Get(CurrentTimeStamp)]

# Commit record
Commit Records/Requests [With dialog: Off]

# Create FileMaker account
Set Variable [$accountName; Value: USERS::username]
Set Variable [$privilegeSet; Value: "[" & USERS::role & "]"]

# Show temporary password to admin
Show Custom Dialog ["User Created"; 
    "User account created successfully." & ¶ &
    "Username: " & USERS::username & ¶ &
    "Temporary Password: " & $tempPassword & ¶ & ¶ &
    "Please provide these credentials to the user and ask them to change the password on first login."]

### Script: User Login Authentication
/**
 * Purpose: Authenticate user login and create session
 */
# Set error capture on
Set Error Capture [On]

# Get login credentials from global fields
Set Variable [$username; Value: Globals::g_login_username]
Set Variable [$password; Value: Globals::g_login_password]

# Clear password field for security
Set Field [Globals::g_login_password; ""]

# Validate credentials
If [IsEmpty($username) or IsEmpty($password)]
    Show Custom Dialog ["Login Error"; "Please enter both username and password."]
    Exit Script [Text Result: "Invalid Credentials"]
End If

# Find user record
Go to Layout ["User_Management"]
Enter Find Mode [Pause: Off]
Set Field [USERS::username; $username]
Set Field [USERS::active_status; 1]
Perform Find [Restore]

If [Get(FoundCount) = 0]
    Show Custom Dialog ["Login Error"; "Invalid username or password."]
    Exit Script [Text Result: "Invalid Credentials"]
End If

# Verify password
Set Variable [$storedHash; Value: USERS::password_hash]
Set Variable [$inputHash; Value: Hash($password; "SHA256")]

If [$storedHash ≠ $inputHash]
    Show Custom Dialog ["Login Error"; "Invalid username or password."]
    Exit Script [Text Result: "Invalid Credentials"]
End If

# Update last login time
Set Field [USERS::last_login; Get(CurrentTimeStamp)]
Commit Records/Requests [With dialog: Off]

# Create user session
Go to Layout ["User_Sessions"]
New Record/Request
Set Field [USER_SESSIONS::user_id; USERS::user_id]
Set Field [USER_SESSIONS::login_time; Get(CurrentTimeStamp)]
Set Field [USER_SESSIONS::ip_address; Get(SystemIPAddress)]
Set Field [USER_SESSIONS::active; 1]
Commit Records/Requests [With dialog: Off]

# Set global user variables
Set Variable [$$CurrentUserID; Value: USERS::user_id]
Set Variable [$$CurrentUsername; Value: USERS::username]
Set Variable [$$CurrentUserRole; Value: USERS::role]
Set Variable [$$SessionID; Value: USER_SESSIONS::session_id]

# Navigate to appropriate dashboard based on role
If [USERS::role = "Admin"]
    Go to Layout ["Admin_Dashboard"]
Else If [USERS::role = "Manager"]
    Go to Layout ["Manager_Dashboard"]
Else If [USERS::role = "Accountant"]
    Go to Layout ["Accountant_Dashboard"]
Else If [USERS::role = "Operator"]
    Go to Layout ["Operator_Dashboard"]
Else
    Go to Layout ["Dashboard"]
End If

# Show welcome message
Show Custom Dialog ["Welcome"; "Welcome, " & USERS::full_name & "!"]

### Script: User Logout
/**
 * Purpose: Safely logout user and close session
 */
# Set error capture on
Set Error Capture [On]

# Update session record
Go to Layout ["User_Sessions"]
Enter Find Mode [Pause: Off]
Set Field [USER_SESSIONS::session_id; $$SessionID]
Set Field [USER_SESSIONS::active; 1]
Perform Find [Restore]

If [Get(FoundCount) > 0]
    Set Field [USER_SESSIONS::logout_time; Get(CurrentTimeStamp)]
    Set Field [USER_SESSIONS::active; 0]
    Commit Records/Requests [With dialog: Off]
End If

# Clear global variables
Set Variable [$$CurrentUserID; Value: ""]
Set Variable [$$CurrentUsername; Value: ""]
Set Variable [$$CurrentUserRole; Value: ""]
Set Variable [$$SessionID; Value: ""]

# Clear global fields
Set Field [Globals::g_login_username; ""]
Set Field [Globals::g_login_password; ""]

# Go to login layout
Go to Layout ["Login"]

# Show logout message
Show Custom Dialog ["Logged Out"; "You have been successfully logged out."]

### Script: Change Password
/**
 * Purpose: Allow users to change their password
 */
# Set error capture on
Set Error Capture [On]

# Validate user is logged in
If [IsEmpty($$CurrentUserID)]
    Show Custom Dialog ["Error"; "Please log in first."]
    Exit Script [Text Result: "Not Logged In"]
End If

# Show password change dialog
Show Custom Dialog ["Change Password"; "Enter your password details:";
    "Current Password:"; Globals::g_current_password;
    "New Password:"; Globals::g_new_password;
    "Confirm New Password:"; Globals::g_confirm_password]

If [Get(LastMessageChoice) = 2]
    # Clear password fields
    Set Field [Globals::g_current_password; ""]
    Set Field [Globals::g_new_password; ""]
    Set Field [Globals::g_confirm_password; ""]
    Exit Script [Text Result: "Cancelled"]
End If

# Validate current password
Go to Layout ["User_Management"]
Enter Find Mode [Pause: Off]
Set Field [USERS::user_id; $$CurrentUserID]
Perform Find [Restore]

Set Variable [$currentHash; Value: Hash(Globals::g_current_password; "SHA256")]
If [$currentHash ≠ USERS::password_hash]
    Show Custom Dialog ["Error"; "Current password is incorrect."]
    # Clear password fields
    Set Field [Globals::g_current_password; ""]
    Set Field [Globals::g_new_password; ""]
    Set Field [Globals::g_confirm_password; ""]
    Exit Script [Text Result: "Invalid Password"]
End If

# Validate new password
If [Globals::g_new_password ≠ Globals::g_confirm_password]
    Show Custom Dialog ["Error"; "New password and confirmation do not match."]
    # Clear password fields
    Set Field [Globals::g_current_password; ""]
    Set Field [Globals::g_new_password; ""]
    Set Field [Globals::g_confirm_password; ""]
    Exit Script [Text Result: "Password Mismatch"]
End If

# Validate password strength
If [Length(Globals::g_new_password) < 6]
    Show Custom Dialog ["Error"; "Password must be at least 6 characters long."]
    # Clear password fields
    Set Field [Globals::g_current_password; ""]
    Set Field [Globals::g_new_password; ""]
    Set Field [Globals::g_confirm_password; ""]
    Exit Script [Text Result: "Weak Password"]
End If

# Update password
Set Field [USERS::password_hash; Hash(Globals::g_new_password; "SHA256")]
Set Field [USERS::modified_date; Get(CurrentTimeStamp)]
Commit Records/Requests [With dialog: Off]

# Clear password fields
Set Field [Globals::g_current_password; ""]
Set Field [Globals::g_new_password; ""]
Set Field [Globals::g_confirm_password; ""]

# Show success message
Show Custom Dialog ["Success"; "Password changed successfully."]

### Script: Check User Permissions
/**
 * Purpose: Check if current user has permission for specific action
 * Parameter: $action (Text) - Action to check permission for
 */
# Set error capture on
Set Error Capture [On]

# Get script parameter
Set Variable [$action; Value: Get(ScriptParameter)]

# Check if user is logged in
If [IsEmpty($$CurrentUserRole)]
    Exit Script [Text Result: "No Access"]
End If

# Define permission matrix
Set Variable [$hasPermission; Value: 
    Case(
        # Admin has access to everything
        $$CurrentUserRole = "Admin"; "Full Access";
        
        # Manager permissions
        $$CurrentUserRole = "Manager" and 
        ($action = "view_reports" or $action = "manage_customers" or 
         $action = "manage_suppliers" or $action = "manage_inventory" or
         $action = "view_financials"); "Allowed";
        
        # Accountant permissions
        $$CurrentUserRole = "Accountant" and 
        ($action = "manage_customers" or $action = "manage_suppliers" or
         $action = "manage_invoices" or $action = "manage_payments" or
         $action = "view_financials" or $action = "generate_reports"); "Allowed";
        
        # Operator permissions
        $$CurrentUserRole = "Operator" and 
        ($action = "view_customers" or $action = "create_invoices" or
         $action = "record_payments" or $action = "manage_inventory" or
         $action = "create_cuts"); "Allowed";
        
        # Default: No access
        "No Access"
    )
]

Exit Script [Text Result: $hasPermission]

### Script: Session Timeout Check
/**
 * Purpose: Check for session timeout and logout inactive users
 */
# Set error capture on
Set Error Capture [On]

# Check if user is logged in
If [IsEmpty($$SessionID)]
    Exit Script [Text Result: "Not Logged In"]
End If

# Get session information
Go to Layout ["User_Sessions"]
Enter Find Mode [Pause: Off]
Set Field [USER_SESSIONS::session_id; $$SessionID]
Set Field [USER_SESSIONS::active; 1]
Perform Find [Restore]

If [Get(FoundCount) = 0]
    # Session not found, force logout
    Perform Script ["User Logout"]
    Exit Script [Text Result: "Session Invalid"]
End If

# Check session timeout (based on user role)
Set Variable [$timeoutMinutes; Value: 
    Case(
        $$CurrentUserRole = "Admin"; 60;
        $$CurrentUserRole = "Manager"; 120;
        $$CurrentUserRole = "Accountant"; 90;
        $$CurrentUserRole = "Operator"; 60;
        60  # Default
    )
]

Set Variable [$sessionAge; Value: 
    (Get(CurrentTimeStamp) - USER_SESSIONS::login_time) / 60]

If [$sessionAge > $timeoutMinutes]
    # Session expired
    Show Custom Dialog ["Session Expired"; 
        "Your session has expired due to inactivity. Please log in again."]
    Perform Script ["User Logout"]
    Exit Script [Text Result: "Session Expired"]
End If

Exit Script [Text Result: "Session Valid"]

## Security Validation Rules

### Username Validation
- Must be unique across all users
- Minimum 3 characters, maximum 20 characters
- Alphanumeric characters only (no spaces or special characters)
- Cannot be changed after creation

### Password Requirements
- Minimum 6 characters
- Must contain at least one letter and one number
- Cannot be the same as username
- Must be changed every 90 days (for Admin and Manager roles)

### Role-Based Field Access
- Admin: Full access to all fields
- Manager: Cannot modify user accounts or system settings
- Accountant: Cannot access operational data (blocks, cuts)
- Operator: Cannot access financial reports or user management

### Audit Trail
- All user actions are logged with timestamp and user ID
- Failed login attempts are tracked
- Password changes are logged
- Data modifications include user ID and timestamp

## Extended Privileges

### fmapp (FileMaker Pro Access)
- Enabled for: Admin, Manager, Accountant, Operator
- Allows access via FileMaker Pro client

### fmxml (XML Web Publishing)
- Enabled for: Admin, Manager
- Allows XML/REST API access for integrations

### fmxslt (XSLT Web Publishing)
- Enabled for: Admin
- Allows XSLT web publishing

### fmreauthenticate10 (Re-authentication)
- Enabled for: All roles
- Requires re-authentication for sensitive operations

## Security Best Practices

1. **Regular Password Updates**: Enforce password changes every 90 days
2. **Session Management**: Automatic logout after inactivity
3. **Audit Logging**: Track all user activities and data changes
4. **Principle of Least Privilege**: Users only have access to necessary functions
5. **Regular Access Reviews**: Quarterly review of user permissions
6. **Secure Password Storage**: All passwords are hashed using SHA-256
7. **Failed Login Protection**: Lock accounts after 5 failed attempts
8. **Data Encryption**: Sensitive data encrypted at rest and in transit
