"""
Al-Hassan <PERSON> Factory - Automation Scripts
Automated tasks and scheduled operations
"""

import schedule
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email import encoders
import pandas as pd
from datetime import datetime, timedelta
import json
import logging
from typing import List, Dict, Any
import os
from database_connector import (
    create_database_connection, 
    CustomerAnalytics, 
    SupplierAnalytics, 
    InventoryAnalytics,
    AlertSystem
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailNotificationSystem:
    """Email notification system for alerts and reports"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
    
    def send_email(self, to_emails: List[str], subject: str, body: str, 
                   attachments: List[str] = None) -> bool:
        """Send email with optional attachments"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = ", ".join(to_emails)
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'html'))
            
            # Add attachments
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        msg.attach(part)
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            text = msg.as_string()
            server.sendmail(self.username, to_emails, text)
            server.quit()
            
            logger.info(f"Email sent successfully to {', '.join(to_emails)}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False

class ReportGenerator:
    """Automated report generation system"""
    
    def __init__(self, db_connector, email_system: EmailNotificationSystem):
        self.db = db_connector
        self.email_system = email_system
        self.customer_analytics = CustomerAnalytics(db_connector)
        self.supplier_analytics = SupplierAnalytics(db_connector)
        self.inventory_analytics = InventoryAnalytics(db_connector)
    
    def generate_daily_summary_report(self) -> str:
        """Generate daily business summary report"""
        try:
            # Get today's data
            today = datetime.now().date()
            
            # Customer metrics
            overdue_customers = self.customer_analytics.get_overdue_customers(30)
            outstanding_balances = self.customer_analytics.get_customer_outstanding_balances()
            
            # Inventory metrics
            block_utilization = self.inventory_analytics.get_block_utilization()
            slow_inventory = self.inventory_analytics.get_slow_moving_inventory(90)
            
            # Generate HTML report
            html_content = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background-color: #2C3E50; color: white; padding: 20px; text-align: center; }}
                    .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
                    .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }}
                    .alert {{ background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                    .success {{ background-color: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Al-Hassan Stone Factory - Daily Summary Report</h1>
                    <p>Report Date: {today.strftime('%B %d, %Y')}</p>
                </div>
                
                <div class="section">
                    <h2>📊 Key Metrics</h2>
                    <div class="metric">
                        <strong>Total Outstanding:</strong><br>
                        {outstanding_balances['outstanding_balance'].sum():,.2f} EGP
                    </div>
                    <div class="metric">
                        <strong>Overdue Customers:</strong><br>
                        {len(overdue_customers)} customers
                    </div>
                    <div class="metric">
                        <strong>Available Blocks:</strong><br>
                        {len(block_utilization[block_utilization['status'] == 'Available'])} blocks
                    </div>
                    <div class="metric">
                        <strong>Slow Moving Items:</strong><br>
                        {len(slow_inventory)} items
                    </div>
                </div>
            """
            
            # Add overdue customers section
            if not overdue_customers.empty:
                html_content += f"""
                <div class="section">
                    <h2>⚠️ Overdue Payments Alert</h2>
                    <div class="alert">
                        <strong>{len(overdue_customers)} customers have overdue payments</strong>
                    </div>
                    <table>
                        <tr>
                            <th>Customer</th>
                            <th>Invoice</th>
                            <th>Due Date</th>
                            <th>Amount Due</th>
                            <th>Days Overdue</th>
                        </tr>
                """
                
                for _, customer in overdue_customers.head(10).iterrows():
                    html_content += f"""
                        <tr>
                            <td>{customer['full_name']}</td>
                            <td>{customer['invoice_number']}</td>
                            <td>{customer['due_date']}</td>
                            <td>{customer['amount_due']:,.2f}</td>
                            <td>{customer['days_overdue']}</td>
                        </tr>
                    """
                
                html_content += "</table></div>"
            
            # Add inventory alerts
            if not slow_inventory.empty:
                html_content += f"""
                <div class="section">
                    <h2>📦 Inventory Alerts</h2>
                    <div class="alert">
                        <strong>{len(slow_inventory)} blocks have been in inventory for over 90 days</strong>
                    </div>
                    <table>
                        <tr>
                            <th>Block ID</th>
                            <th>Type</th>
                            <th>Color</th>
                            <th>Days in Inventory</th>
                            <th>Value</th>
                        </tr>
                """
                
                for _, block in slow_inventory.head(10).iterrows():
                    html_content += f"""
                        <tr>
                            <td>{block['block_id']}</td>
                            <td>{block['block_type']}</td>
                            <td>{block['color']}</td>
                            <td>{block['days_in_inventory']}</td>
                            <td>{block['total_cost']:,.2f}</td>
                        </tr>
                    """
                
                html_content += "</table></div>"
            
            html_content += """
                <div class="section">
                    <h2>📈 Recommendations</h2>
                    <ul>
                        <li>Follow up with overdue customers for payment collection</li>
                        <li>Review slow-moving inventory for potential promotions</li>
                        <li>Monitor block utilization rates for optimization opportunities</li>
                        <li>Consider supplier performance reviews for cost optimization</li>
                    </ul>
                </div>
                
                <div class="section">
                    <p><em>This report was automatically generated by the Al-Hassan Stone Factory Management System.</em></p>
                </div>
            </body>
            </html>
            """
            
            # Save report to file
            report_filename = f"daily_report_{today.strftime('%Y%m%d')}.html"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Daily summary report generated: {report_filename}")
            return report_filename
            
        except Exception as e:
            logger.error(f"Failed to generate daily summary report: {str(e)}")
            return None
    
    def generate_weekly_financial_report(self) -> str:
        """Generate weekly financial performance report"""
        try:
            # Get week's data
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)
            
            # Get financial data
            query = """
            SELECT 
                'Sales' as category,
                SUM(net_amount) as amount,
                COUNT(*) as count
            FROM INVOICES 
            WHERE invoice_date BETWEEN ? AND ?
            UNION ALL
            SELECT 
                'Payments Received' as category,
                SUM(amount) as amount,
                COUNT(*) as count
            FROM PAYMENTS 
            WHERE payment_date BETWEEN ? AND ?
            UNION ALL
            SELECT 
                'Supplier Payments' as category,
                SUM(amount) as amount,
                COUNT(*) as count
            FROM SUPPLIER_PAYMENTS 
            WHERE payment_date BETWEEN ? AND ?
            """
            
            financial_data = self.db.execute_query(query, (start_date, end_date, start_date, end_date, start_date, end_date))
            
            # Generate Excel report
            report_filename = f"weekly_financial_report_{end_date.strftime('%Y%m%d')}.xlsx"
            
            with pd.ExcelWriter(report_filename, engine='openpyxl') as writer:
                # Financial summary
                financial_data.to_excel(writer, sheet_name='Financial Summary', index=False)
                
                # Customer outstanding balances
                outstanding = self.customer_analytics.get_customer_outstanding_balances()
                outstanding.to_excel(writer, sheet_name='Outstanding Balances', index=False)
                
                # Top customers
                top_customers = self.customer_analytics.get_top_customers(20, 3)
                top_customers.to_excel(writer, sheet_name='Top Customers', index=False)
                
                # Supplier performance
                supplier_performance = self.supplier_analytics.get_supplier_performance()
                supplier_performance.to_excel(writer, sheet_name='Supplier Performance', index=False)
            
            logger.info(f"Weekly financial report generated: {report_filename}")
            return report_filename
            
        except Exception as e:
            logger.error(f"Failed to generate weekly financial report: {str(e)}")
            return None
    
    def generate_monthly_inventory_report(self) -> str:
        """Generate monthly inventory analysis report"""
        try:
            # Get inventory data
            block_utilization = self.inventory_analytics.get_block_utilization()
            slow_inventory = self.inventory_analytics.get_slow_moving_inventory(60)
            inventory_predictions = self.inventory_analytics.predict_inventory_needs()
            
            # Generate Excel report
            report_filename = f"monthly_inventory_report_{datetime.now().strftime('%Y%m')}.xlsx"
            
            with pd.ExcelWriter(report_filename, engine='openpyxl') as writer:
                # Block utilization
                block_utilization.to_excel(writer, sheet_name='Block Utilization', index=False)
                
                # Slow moving inventory
                slow_inventory.to_excel(writer, sheet_name='Slow Moving Items', index=False)
                
                # Inventory recommendations
                if inventory_predictions.get('inventory_recommendations'):
                    recommendations_df = pd.DataFrame(inventory_predictions['inventory_recommendations'])
                    recommendations_df.to_excel(writer, sheet_name='Reorder Recommendations', index=False)
            
            logger.info(f"Monthly inventory report generated: {report_filename}")
            return report_filename
            
        except Exception as e:
            logger.error(f"Failed to generate monthly inventory report: {str(e)}")
            return None

class AutomationScheduler:
    """Main automation scheduler for all tasks"""
    
    def __init__(self):
        self.db = create_database_connection()
        self.email_system = EmailNotificationSystem(
            smtp_server="smtp.gmail.com",  # Configure your SMTP server
            smtp_port=587,
            username="<EMAIL>",  # Configure your email
            password="your_app_password"  # Use app password for Gmail
        )
        self.report_generator = ReportGenerator(self.db, self.email_system)
        self.alert_system = AlertSystem(self.db)
    
    def run_daily_tasks(self):
        """Execute daily automated tasks"""
        logger.info("Starting daily automation tasks...")
        
        try:
            # Generate daily alerts
            alerts = self.alert_system.generate_daily_alerts()
            
            # Send high-priority alerts immediately
            high_priority_alerts = [alert for alert in alerts if alert['severity'] == 'high']
            if high_priority_alerts:
                alert_body = self._format_alerts_email(high_priority_alerts)
                self.email_system.send_email(
                    to_emails=["<EMAIL>"],  # Configure recipient
                    subject="🚨 Urgent: High Priority Business Alerts",
                    body=alert_body
                )
            
            # Generate and send daily summary report
            report_file = self.report_generator.generate_daily_summary_report()
            if report_file:
                self.email_system.send_email(
                    to_emails=["<EMAIL>"],  # Configure recipients
                    subject=f"Daily Summary Report - {datetime.now().strftime('%B %d, %Y')}",
                    body="Please find attached the daily summary report.",
                    attachments=[report_file]
                )
            
            logger.info("Daily automation tasks completed successfully")
            
        except Exception as e:
            logger.error(f"Daily automation tasks failed: {str(e)}")
    
    def run_weekly_tasks(self):
        """Execute weekly automated tasks"""
        logger.info("Starting weekly automation tasks...")
        
        try:
            # Generate weekly financial report
            report_file = self.report_generator.generate_weekly_financial_report()
            if report_file:
                self.email_system.send_email(
                    to_emails=["<EMAIL>"],  # Configure recipients
                    subject=f"Weekly Financial Report - Week of {datetime.now().strftime('%B %d, %Y')}",
                    body="Please find attached the weekly financial performance report.",
                    attachments=[report_file]
                )
            
            logger.info("Weekly automation tasks completed successfully")
            
        except Exception as e:
            logger.error(f"Weekly automation tasks failed: {str(e)}")
    
    def run_monthly_tasks(self):
        """Execute monthly automated tasks"""
        logger.info("Starting monthly automation tasks...")
        
        try:
            # Generate monthly inventory report
            report_file = self.report_generator.generate_monthly_inventory_report()
            if report_file:
                self.email_system.send_email(
                    to_emails=["<EMAIL>"],  # Configure recipients
                    subject=f"Monthly Inventory Report - {datetime.now().strftime('%B %Y')}",
                    body="Please find attached the monthly inventory analysis report.",
                    attachments=[report_file]
                )
            
            # Run comprehensive analytics
            self._run_monthly_analytics()
            
            logger.info("Monthly automation tasks completed successfully")
            
        except Exception as e:
            logger.error(f"Monthly automation tasks failed: {str(e)}")
    
    def _format_alerts_email(self, alerts: List[Dict[str, Any]]) -> str:
        """Format alerts for email notification"""
        html_body = """
        <html>
        <body>
            <h2>🚨 High Priority Business Alerts</h2>
            <p>The following alerts require immediate attention:</p>
        """
        
        for alert in alerts:
            html_body += f"""
            <div style="border: 1px solid #dc3545; padding: 10px; margin: 10px 0; background-color: #f8d7da;">
                <h3>{alert['type'].replace('_', ' ').title()}</h3>
                <p>{alert['message']}</p>
            </div>
            """
        
        html_body += """
            <p>Please log into the Al-Hassan Stone Factory Management System for detailed information.</p>
        </body>
        </html>
        """
        
        return html_body
    
    def _run_monthly_analytics(self):
        """Run comprehensive monthly analytics"""
        try:
            # Customer behavior analysis
            customer_analytics = CustomerAnalytics(self.db)
            top_customers = customer_analytics.get_top_customers(50, 12)
            
            # Supplier cost analysis
            supplier_analytics = SupplierAnalytics(self.db)
            cost_savings = supplier_analytics.identify_cost_savings_opportunities()
            
            # Log insights
            logger.info(f"Monthly analytics: {len(top_customers)} top customers analyzed")
            logger.info(f"Monthly analytics: {len(cost_savings)} cost saving opportunities identified")
            
        except Exception as e:
            logger.error(f"Monthly analytics failed: {str(e)}")
    
    def start_scheduler(self):
        """Start the automation scheduler"""
        logger.info("Starting automation scheduler...")
        
        # Schedule daily tasks
        schedule.every().day.at("08:00").do(self.run_daily_tasks)
        
        # Schedule weekly tasks (every Monday)
        schedule.every().monday.at("09:00").do(self.run_weekly_tasks)
        
        # Schedule monthly tasks (first day of month)
        schedule.every().month.do(self.run_monthly_tasks)
        
        # Keep the scheduler running
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

def main():
    """Main entry point for automation system"""
    scheduler = AutomationScheduler()
    
    try:
        scheduler.start_scheduler()
    except KeyboardInterrupt:
        logger.info("Automation scheduler stopped by user")
    except Exception as e:
        logger.error(f"Automation scheduler failed: {str(e)}")
    finally:
        scheduler.db.disconnect()

if __name__ == "__main__":
    main()
