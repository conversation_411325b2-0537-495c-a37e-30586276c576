# Al-Hassan Stone Factory - Comprehensive Reporting System
# FileMaker Pro Reports and Analytics

## Customer Reports

### Script: Generate Customer Account Statement
/**
 * Purpose: Generate detailed customer account statement
 */
# Set error capture on
Set Error Capture [On]

# Validate customer selection
If [IsEmpty(CUSTOMERS::customer_id)]
    Show Custom Dialog ["Error"; "Please select a customer first."]
    Exit Script [Text Result: "Error"]
End If

# Set date range dialog
Show Custom Dialog ["Statement Period"; "Select statement period:";
    "From Date:"; Globals::g_date_from;
    "To Date:"; Globals::g_date_to]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Validate date range
If [IsEmpty(Globals::g_date_from) or IsEmpty(Globals::g_date_to)]
    Show Custom Dialog ["Error"; "Please enter both from and to dates."]
    Exit Script [Text Result: "Error"]
End If

If [Globals::g_date_from > Globals::g_date_to]
    Show Custom Dialog ["Error"; "From date cannot be later than to date."]
    Exit Script [Text Result: "Error"]
End If

# Calculate statement data
Set Variable [$customerID; Value: CUSTOMERS::customer_id]
Set Variable [$fromDate; Value: Globals::g_date_from]
Set Variable [$toDate; Value: Globals::g_date_to]

# Get opening balance (transactions before from date)
Set Variable [$openingInvoices; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(net_amount), 0) FROM INVOICES WHERE customer_id = ? AND invoice_date < ?";
    ""; ""; $customerID; $fromDate)]

Set Variable [$openingPayments; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(amount), 0) FROM PAYMENTS WHERE customer_id = ? AND payment_date < ?";
    ""; ""; $customerID; $fromDate)]

Set Variable [$openingBalance; Value: $openingInvoices - $openingPayments]

# Get period transactions
Set Variable [$periodInvoices; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(net_amount), 0) FROM INVOICES WHERE customer_id = ? AND invoice_date BETWEEN ? AND ?";
    ""; ""; $customerID; $fromDate; $toDate)]

Set Variable [$periodPayments; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(amount), 0) FROM PAYMENTS WHERE customer_id = ? AND payment_date BETWEEN ? AND ?";
    ""; ""; $customerID; $fromDate; $toDate)]

Set Variable [$closingBalance; Value: $openingBalance + $periodInvoices - $periodPayments]

# Create report record
Go to Layout ["Customer_Reports"]
New Record/Request

Set Field [CUSTOMER_REPORTS::customer_id; $customerID]
Set Field [CUSTOMER_REPORTS::report_type; "Account Statement"]
Set Field [CUSTOMER_REPORTS::report_date; Get(CurrentDate)]
Set Field [CUSTOMER_REPORTS::total_sales; $periodInvoices]
Set Field [CUSTOMER_REPORTS::total_payments; $periodPayments]
Set Field [CUSTOMER_REPORTS::outstanding_balance; $closingBalance]

# Generate detailed report data
Set Variable [$reportData; Value: 
    "{" &
    "\"customer_id\":\"" & $customerID & "\"," &
    "\"customer_name\":\"" & CUSTOMERS::full_name & "\"," &
    "\"statement_period\":{" &
        "\"from\":\"" & $fromDate & "\"," &
        "\"to\":\"" & $toDate & "\"" &
    "}," &
    "\"opening_balance\":" & $openingBalance & "," &
    "\"period_invoices\":" & $periodInvoices & "," &
    "\"period_payments\":" & $periodPayments & "," &
    "\"closing_balance\":" & $closingBalance &
    "}"
]

Set Field [CUSTOMER_REPORTS::report_data; $reportData]
Set Field [CUSTOMER_REPORTS::created_date; Get(CurrentTimeStamp)]

Commit Records/Requests [With dialog: Off]

# Go to statement layout for printing
Go to Layout ["Customer_Statement_Print"]

# Set global fields for report display
Set Field [Globals::g_report_customer_name; CUSTOMERS::full_name]
Set Field [Globals::g_report_from_date; $fromDate]
Set Field [Globals::g_report_to_date; $toDate]
Set Field [Globals::g_report_opening_balance; $openingBalance]
Set Field [Globals::g_report_period_sales; $periodInvoices]
Set Field [Globals::g_report_period_payments; $periodPayments]
Set Field [Globals::g_report_closing_balance; $closingBalance]

# Print or preview statement
Show Custom Dialog ["Print Statement"; "Would you like to print or preview the statement?";
    "Print"; "Preview"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else
    Enter Preview Mode [Pause: On]
End If

### Script: Customer Sales Analysis Report
/**
 * Purpose: Generate customer sales analysis and ranking report
 */
# Set error capture on
Set Error Capture [On]

# Set analysis period
Show Custom Dialog ["Sales Analysis Period"; "Select analysis period:";
    "From Date:"; Globals::g_date_from;
    "To Date:"; Globals::g_date_to;
    "Minimum Sales Amount:"; Globals::g_min_amount]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set default values if empty
If [IsEmpty(Globals::g_date_from)]
    Set Field [Globals::g_date_from; Date(1; 1; Year(Get(CurrentDate)))]
End If

If [IsEmpty(Globals::g_date_to)]
    Set Field [Globals::g_date_to; Get(CurrentDate)]
End If

If [IsEmpty(Globals::g_min_amount)]
    Set Field [Globals::g_min_amount; 0]
End If

# Go to customer list layout
Go to Layout ["Customer_List"]

# Find customers with sales in period
Enter Find Mode [Pause: Off]
Set Field [INVOICES::invoice_date; Globals::g_date_from & ".." & Globals::g_date_to]
Perform Find [Restore]

# Sort by total sales (descending)
Sort Records [Restore; With dialog: Off]
    [Sum(INVOICES::net_amount); descending]

# Go to sales analysis report layout
Go to Layout ["Customer_Sales_Analysis"]

# Print or preview report
Show Custom Dialog ["Sales Analysis Report"; "Customer sales analysis is ready.";
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Customer_Sales_Analysis.xlsx"]
End If

### Script: Overdue Customers Report
/**
 * Purpose: Generate report of customers with overdue payments
 */
# Set error capture on
Set Error Capture [On]

# Set overdue criteria
Show Custom Dialog ["Overdue Criteria"; "Set overdue payment criteria:";
    "Days Overdue (minimum):"; Globals::g_days_overdue;
    "Minimum Amount:"; Globals::g_min_overdue_amount]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set default values
If [IsEmpty(Globals::g_days_overdue)]
    Set Field [Globals::g_days_overdue; 30]
End If

If [IsEmpty(Globals::g_min_overdue_amount)]
    Set Field [Globals::g_min_overdue_amount; 100]
End If

# Calculate cutoff date
Set Variable [$cutoffDate; Value: Get(CurrentDate) - Globals::g_days_overdue]

# Go to invoice layout
Go to Layout ["Invoice_List"]

# Find overdue invoices
Enter Find Mode [Pause: Off]
Set Field [INVOICES::due_date; "<" & $cutoffDate]
Set Field [INVOICES::status; "Sent"]
New Record/Request
Set Field [INVOICES::status; "Partial Payment"]
Perform Find [Restore]

# Filter by minimum amount
If [Globals::g_min_overdue_amount > 0]
    Constrain Found Set [Restore]
    Enter Find Mode [Pause: Off]
    Set Field [INVOICES::net_amount; ">=" & Globals::g_min_overdue_amount]
    Perform Find [Restore]
End If

# Sort by days overdue (descending)
Sort Records [Restore; With dialog: Off]
    [INVOICES::due_date; ascending]

# Go to overdue report layout
Go to Layout ["Overdue_Customers_Report"]

# Print or preview report
Show Custom Dialog ["Overdue Customers Report"; "Overdue customers report is ready.";
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Overdue_Customers.xlsx"]
End If

## Supplier Reports

### Script: Supplier Performance Report
/**
 * Purpose: Generate comprehensive supplier performance analysis
 */
# Set error capture on
Set Error Capture [On]

# Set analysis period
Show Custom Dialog ["Supplier Performance Analysis"; "Select analysis period:";
    "From Date:"; Globals::g_date_from;
    "To Date:"; Globals::g_date_to]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set default to last 12 months if empty
If [IsEmpty(Globals::g_date_from)]
    Set Field [Globals::g_date_from; Get(CurrentDate) - 365]
End If

If [IsEmpty(Globals::g_date_to)]
    Set Field [Globals::g_date_to; Get(CurrentDate)]
End If

# Go to supplier list layout
Go to Layout ["Supplier_List"]

# Show all active suppliers
Enter Find Mode [Pause: Off]
Set Field [SUPPLIERS::active_status; 1]
Perform Find [Restore]

# Calculate performance metrics for each supplier
Go to Record/Request/Page [First]
Loop
    # Calculate supplier metrics
    Set Variable [$supplierID; Value: SUPPLIERS::supplier_id]
    
    # Total orders
    Set Variable [$totalOrders; Value: ExecuteSQL(
        "SELECT COUNT(*) FROM PURCHASE_ORDERS WHERE supplier_id = ? AND order_date BETWEEN ? AND ?";
        ""; ""; $supplierID; Globals::g_date_from; Globals::g_date_to)]
    
    # Total amount
    Set Variable [$totalAmount; Value: ExecuteSQL(
        "SELECT COALESCE(SUM(total_amount), 0) FROM PURCHASE_ORDERS WHERE supplier_id = ? AND order_date BETWEEN ? AND ?";
        ""; ""; $supplierID; Globals::g_date_from; Globals::g_date_to)]
    
    # Completed orders
    Set Variable [$completedOrders; Value: ExecuteSQL(
        "SELECT COUNT(*) FROM PURCHASE_ORDERS WHERE supplier_id = ? AND status = 'Received' AND order_date BETWEEN ? AND ?";
        ""; ""; $supplierID; Globals::g_date_from; Globals::g_date_to)]
    
    # Calculate completion rate
    Set Variable [$completionRate; Value: 
        If($totalOrders > 0; Round(($completedOrders / $totalOrders) * 100; 1); 0)]
    
    # Store metrics in global fields for report
    Set Field [Globals::g_supplier_total_orders; $totalOrders]
    Set Field [Globals::g_supplier_total_amount; $totalAmount]
    Set Field [Globals::g_supplier_completion_rate; $completionRate]
    
    # Go to next supplier
    Go to Record/Request/Page [Next; Exit after last]
End Loop

# Sort by total purchase amount (descending)
Sort Records [Restore; With dialog: Off]
    [Globals::g_supplier_total_amount; descending]

# Go to supplier performance report layout
Go to Layout ["Supplier_Performance_Report"]

# Print or preview report
Show Custom Dialog ["Supplier Performance Report"; "Supplier performance report is ready.";
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Supplier_Performance.xlsx"]
End If

### Script: Supplier Cost Analysis Report
/**
 * Purpose: Analyze supplier costs and identify savings opportunities
 */
# Set error capture on
Set Error Capture [On]

# Set analysis parameters
Show Custom Dialog ["Cost Analysis Parameters"; "Set cost analysis parameters:";
    "Analysis Period (months):"; Globals::g_analysis_months;
    "Supplier Type Filter:"; Globals::g_supplier_type_filter]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set defaults
If [IsEmpty(Globals::g_analysis_months)]
    Set Field [Globals::g_analysis_months; 12]
End If

# Calculate date range
Set Variable [$fromDate; Value: Get(CurrentDate) - (Globals::g_analysis_months * 30)]
Set Variable [$toDate; Value: Get(CurrentDate)]

# Go to supplier list layout
Go to Layout ["Supplier_List"]

# Filter by supplier type if specified
If [not IsEmpty(Globals::g_supplier_type_filter)]
    Enter Find Mode [Pause: Off]
    Set Field [SUPPLIERS::supplier_type; Globals::g_supplier_type_filter]
    Set Field [SUPPLIERS::active_status; 1]
    Perform Find [Restore]
Else
    Enter Find Mode [Pause: Off]
    Set Field [SUPPLIERS::active_status; 1]
    Perform Find [Restore]
End If

# Calculate cost metrics for each supplier
Go to Record/Request/Page [First]
Loop
    Set Variable [$supplierID; Value: SUPPLIERS::supplier_id]
    
    # Monthly cost breakdown
    Set Variable [$monthlyCosts; Value: ExecuteSQL(
        "SELECT YEAR(order_date), MONTH(order_date), SUM(total_amount) " &
        "FROM PURCHASE_ORDERS " &
        "WHERE supplier_id = ? AND order_date BETWEEN ? AND ? " &
        "GROUP BY YEAR(order_date), MONTH(order_date) " &
        "ORDER BY YEAR(order_date), MONTH(order_date)";
        ""; ""; $supplierID; $fromDate; $toDate)]
    
    # Average unit costs
    Set Variable [$avgUnitCost; Value: ExecuteSQL(
        "SELECT AVG(unit_price) " &
        "FROM PURCHASE_ORDER_ITEMS poi " &
        "INNER JOIN PURCHASE_ORDERS po ON poi.po_id = po.po_id " &
        "WHERE po.supplier_id = ? AND po.order_date BETWEEN ? AND ?";
        ""; ""; $supplierID; $fromDate; $toDate)]
    
    # Store in global fields
    Set Field [Globals::g_supplier_avg_unit_cost; $avgUnitCost]
    
    Go to Record/Request/Page [Next; Exit after last]
End Loop

# Go to cost analysis report layout
Go to Layout ["Supplier_Cost_Analysis_Report"]

# Print or preview report
Show Custom Dialog ["Cost Analysis Report"; "Supplier cost analysis report is ready.";
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Supplier_Cost_Analysis.xlsx"]
End If

## Inventory Reports

### Script: Block Utilization Report
/**
 * Purpose: Generate block utilization and efficiency report
 */
# Set error capture on
Set Error Capture [On]

# Set report parameters
Show Custom Dialog ["Block Utilization Report"; "Set report parameters:";
    "Block Type Filter:"; Globals::g_block_type_filter;
    "Location Filter:"; Globals::g_location_filter;
    "Minimum Utilization %:"; Globals::g_min_utilization]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Go to blocks layout
Go to Layout ["Block_List"]

# Apply filters
Enter Find Mode [Pause: Off]

If [not IsEmpty(Globals::g_block_type_filter)]
    Set Field [BLOCKS::block_type; "*" & Globals::g_block_type_filter & "*"]
End If

If [not IsEmpty(Globals::g_location_filter)]
    Set Field [BLOCKS::location; "*" & Globals::g_location_filter & "*"]
End If

Perform Find [Restore]

# Calculate utilization for each block
Go to Record/Request/Page [First]
Loop
    Set Variable [$blockID; Value: BLOCKS::block_id]
    Set Variable [$totalVolume; Value: BLOCKS::volume]
    
    # Calculate used volume
    Set Variable [$usedVolume; Value: ExecuteSQL(
        "SELECT COALESCE(SUM(area * thickness), 0) FROM CUTS WHERE block_id = ?";
        ""; ""; $blockID)]
    
    # Calculate utilization percentage
    Set Variable [$utilizationRate; Value: 
        If($totalVolume > 0; Round(($usedVolume / $totalVolume) * 100; 2); 0)]
    
    # Store in global field
    Set Field [Globals::g_block_utilization_rate; $utilizationRate]
    
    Go to Record/Request/Page [Next; Exit after last]
End Loop

# Filter by minimum utilization if specified
If [not IsEmpty(Globals::g_min_utilization)]
    Constrain Found Set [Restore]
    Enter Find Mode [Pause: Off]
    Set Field [Globals::g_block_utilization_rate; ">=" & Globals::g_min_utilization]
    Perform Find [Restore]
End If

# Sort by utilization rate (descending)
Sort Records [Restore; With dialog: Off]
    [Globals::g_block_utilization_rate; descending]

# Go to utilization report layout
Go to Layout ["Block_Utilization_Report"]

# Print or preview report
Show Custom Dialog ["Block Utilization Report"; "Block utilization report is ready.";
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Block_Utilization.xlsx"]
End If

### Script: Inventory Valuation Report
/**
 * Purpose: Generate current inventory valuation report
 */
# Set error capture on
Set Error Capture [On]

# Set valuation date
Show Custom Dialog ["Inventory Valuation"; "Set valuation parameters:";
    "Valuation Date:"; Globals::g_valuation_date;
    "Include Depleted Blocks:"; Globals::g_include_depleted]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set default valuation date to today
If [IsEmpty(Globals::g_valuation_date)]
    Set Field [Globals::g_valuation_date; Get(CurrentDate)]
End If

# Go to blocks layout
Go to Layout ["Block_List"]

# Find blocks based on criteria
Enter Find Mode [Pause: Off]

If [Globals::g_include_depleted = "Yes"]
    # Include all blocks
    Set Field [BLOCKS::status; "*"]
Else
    # Only available and in-use blocks
    Set Field [BLOCKS::status; "Available"]
    New Record/Request
    Set Field [BLOCKS::status; "In Use"]
End If

Perform Find [Restore]

# Calculate total inventory value
Set Variable [$totalValue; Value: 0]
Set Variable [$totalBlocks; Value: Get(FoundCount)]

Go to Record/Request/Page [First]
Loop
    Set Variable [$blockValue; Value: BLOCKS::total_cost]
    Set Variable [$totalValue; Value: $totalValue + $blockValue]
    
    Go to Record/Request/Page [Next; Exit after last]
End Loop

# Group by block type for summary
Sort Records [Restore; With dialog: Off]
    [BLOCKS::block_type; ascending]
    [BLOCKS::color; ascending]

# Go to inventory valuation report layout
Go to Layout ["Inventory_Valuation_Report"]

# Set summary fields
Set Field [Globals::g_total_inventory_value; $totalValue]
Set Field [Globals::g_total_blocks_count; $totalBlocks]
Set Field [Globals::g_valuation_date_display; Globals::g_valuation_date]

# Print or preview report
Show Custom Dialog ["Inventory Valuation Report"; 
    "Total Inventory Value: " & $totalValue & " EGP" & ¶ &
    "Total Blocks: " & $totalBlocks;
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Inventory_Valuation.xlsx"]
End If

## Financial Reports

### Script: Profit and Loss Report
/**
 * Purpose: Generate comprehensive P&L statement
 */
# Set error capture on
Set Error Capture [On]

# Set reporting period
Show Custom Dialog ["P&L Report Period"; "Select reporting period:";
    "From Date:"; Globals::g_date_from;
    "To Date:"; Globals::g_date_to;
    "Currency:"; Globals::g_report_currency]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set defaults
If [IsEmpty(Globals::g_date_from)]
    Set Field [Globals::g_date_from; Date(1; 1; Year(Get(CurrentDate)))]
End If

If [IsEmpty(Globals::g_date_to)]
    Set Field [Globals::g_date_to; Get(CurrentDate)]
End If

If [IsEmpty(Globals::g_report_currency)]
    Set Field [Globals::g_report_currency; "EGP"]
End If

# Calculate revenue
Set Variable [$totalRevenue; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(net_amount), 0) FROM INVOICES " &
    "WHERE invoice_date BETWEEN ? AND ? AND currency = ?";
    ""; ""; Globals::g_date_from; Globals::g_date_to; Globals::g_report_currency)]

# Calculate cost of goods sold (block costs for cuts sold)
Set Variable [$cogs; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(c.total_cost), 0) " &
    "FROM CUTS c " &
    "INNER JOIN INVOICE_ITEMS ii ON c.cut_id = ii.cut_id " &
    "INNER JOIN INVOICES i ON ii.invoice_id = i.invoice_id " &
    "WHERE i.invoice_date BETWEEN ? AND ? AND i.currency = ?";
    ""; ""; Globals::g_date_from; Globals::g_date_to; Globals::g_report_currency)]

# Calculate operating expenses
Set Variable [$operatingExpenses; Value: ExecuteSQL(
    "SELECT COALESCE(SUM(amount), 0) FROM EXPENSES " &
    "WHERE expense_date BETWEEN ? AND ? AND currency = ?";
    ""; ""; Globals::g_date_from; Globals::g_date_to; Globals::g_report_currency)]

# Calculate gross profit and net profit
Set Variable [$grossProfit; Value: $totalRevenue - $cogs]
Set Variable [$netProfit; Value: $grossProfit - $operatingExpenses]

# Set global fields for report display
Set Field [Globals::g_pl_total_revenue; $totalRevenue]
Set Field [Globals::g_pl_cogs; $cogs]
Set Field [Globals::g_pl_gross_profit; $grossProfit]
Set Field [Globals::g_pl_operating_expenses; $operatingExpenses]
Set Field [Globals::g_pl_net_profit; $netProfit]

# Go to P&L report layout
Go to Layout ["Profit_Loss_Report"]

# Print or preview report
Show Custom Dialog ["Profit & Loss Report"; 
    "Net Profit: " & $netProfit & " " & Globals::g_report_currency;
    "Print"; "Preview"; "Export to Excel"]

If [Get(LastMessageChoice) = 1]
    Print [With dialog: Off]
Else If [Get(LastMessageChoice) = 2]
    Enter Preview Mode [Pause: On]
Else If [Get(LastMessageChoice) = 3]
    Export Records [With dialog: On; "Profit_Loss_Report.xlsx"]
End If
