# Al-Hassan <PERSON> Factory Management System - Installation Guide

## Overview

This guide provides step-by-step instructions for installing and configuring the Al-Hassan Stone Factory Management System. The system consists of a FileMaker Pro database with Python integration for advanced analytics.

## System Requirements

### Hardware Requirements
- **Processor**: Intel Core i5 or AMD equivalent (minimum)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB available disk space
- **Network**: Ethernet connection for multi-user setup
- **Display**: 1920x1080 resolution minimum

### Software Requirements

#### FileMaker Pro
- **FileMaker Pro 19 or later** (required)
- **FileMaker Server 19** (for multi-user deployment)
- **Windows 10/11** or **macOS 10.15+**

#### Python Environment (Optional - for analytics)
- **Python 3.8 or later**
- **pip package manager**
- **Virtual environment support**

#### Additional Software
- **Microsoft Excel** (for report exports)
- **PDF reader** (for documentation)
- **Email client** (for notifications)

## Pre-Installation Checklist

### 1. Verify System Requirements
- [ ] Check hardware specifications
- [ ] Confirm operating system compatibility
- [ ] Verify FileMaker Pro licensing
- [ ] Test network connectivity (for multi-user)

### 2. Prepare Installation Environment
- [ ] Create backup of existing data
- [ ] Close all unnecessary applications
- [ ] Disable antivirus temporarily (if needed)
- [ ] Ensure administrator privileges

### 3. Gather Required Information
- [ ] FileMaker Pro license key
- [ ] Network configuration details
- [ ] User account information
- [ ] Email server settings (for notifications)

## Installation Steps

### Step 1: Install FileMaker Pro

#### Windows Installation
1. Download FileMaker Pro from Claris website
2. Run the installer as administrator
3. Follow installation wizard
4. Enter license key when prompted
5. Complete installation and restart if required

#### macOS Installation
1. Download FileMaker Pro .dmg file
2. Mount the disk image
3. Drag FileMaker Pro to Applications folder
4. Launch and enter license key
5. Complete setup process

### Step 2: Install Database Files

1. **Download Database Package**
   - Extract `AlHassanStone_v1.0.zip`
   - Verify all files are present:
     - `AlHassanStone.fmp12` (main database)
     - `database_schema.md` (documentation)
     - `python_integration/` (analytics folder)
     - `user_manual.md` (user guide)

2. **Place Database Files**
   - Create folder: `C:\AlHassanStone\` (Windows) or `/Applications/AlHassanStone/` (macOS)
   - Copy `AlHassanStone.fmp12` to this folder
   - Copy all documentation files

3. **Set File Permissions**
   - Ensure read/write access for all users
   - Set appropriate network sharing permissions

### Step 3: Initial Database Configuration

1. **Open Database**
   - Launch FileMaker Pro
   - Open `AlHassanStone.fmp12`
   - Database will open to login screen

2. **First-Time Login**
   - Username: `admin`
   - Password: `AlHassan2024`
   - Click "Login"

3. **Change Default Password**
   - Go to File → Manage → Security
   - Select admin account
   - Change password to secure password
   - Save changes

4. **Configure Basic Settings**
   - Set company information
   - Configure default currency
   - Set up initial value lists
   - Configure email settings

### Step 4: Create User Accounts

1. **Access User Management**
   - Navigate to Admin Dashboard
   - Click "User Management"
   - Click "New User"

2. **Create Manager Account**
   - Username: `manager`
   - Full Name: [Manager's Name]
   - Role: Manager
   - Set temporary password
   - Save account

3. **Create Additional Accounts**
   - Repeat for Accountant and Operator roles
   - Provide credentials to respective users
   - Require password change on first login

### Step 5: Import Initial Data (Optional)

1. **Prepare Data Files**
   - Customer data (CSV format)
   - Supplier data (CSV format)
   - Initial inventory (if applicable)

2. **Import Process**
   - Go to File → Import Records
   - Select data source
   - Map fields correctly
   - Validate imported data

### Step 6: Python Integration Setup (Optional)

#### Install Python
1. **Download Python**
   - Visit python.org
   - Download Python 3.8 or later
   - Run installer with "Add to PATH" option

2. **Verify Installation**
   ```bash
   python --version
   pip --version
   ```

#### Install Dependencies
1. **Navigate to Integration Folder**
   ```bash
   cd C:\AlHassanStone\python_integration\
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   source venv/bin/activate  # macOS/Linux
   ```

3. **Install Required Packages**
   ```bash
   pip install pandas numpy matplotlib seaborn
   pip install pyodbc schedule
   pip install openpyxl xlsxwriter
   ```

#### Configure Database Connection
1. **Edit Configuration File**
   - Open `database_connector.py`
   - Update database connection settings:
   ```python
   config = DatabaseConfig(
       server="localhost",  # FileMaker Server IP
       database="AlHassanStone.fmp12",
       username="python_user",  # Create dedicated user
       password="secure_password",
       driver="FileMaker ODBC"
   )
   ```

2. **Test Connection**
   ```bash
   python database_connector.py
   ```

#### Configure Email Notifications
1. **Edit Email Settings**
   - Open `automation_scripts.py`
   - Update SMTP configuration:
   ```python
   email_system = EmailNotificationSystem(
       smtp_server="smtp.gmail.com",
       smtp_port=587,
       username="<EMAIL>",
       password="app_password"
   )
   ```

2. **Test Email System**
   ```bash
   python -c "from automation_scripts import EmailNotificationSystem; print('Email system ready')"
   ```

## Multi-User Deployment

### FileMaker Server Installation

1. **Install FileMaker Server**
   - Download from Claris website
   - Run installer on server machine
   - Configure server settings
   - Set up SSL certificates

2. **Deploy Database**
   - Upload `AlHassanStone.fmp12` to server
   - Configure backup schedules
   - Set up monitoring

3. **Configure Client Access**
   - Install FileMaker Pro on client machines
   - Configure network connections
   - Test connectivity

### Network Configuration

1. **Firewall Settings**
   - Open port 5003 (FileMaker Pro)
   - Open port 443 (HTTPS)
   - Configure internal network access

2. **User Access**
   - Create network user accounts
   - Configure group policies
   - Set up remote access (if needed)

## Post-Installation Configuration

### 1. System Validation

#### Test Core Functions
- [ ] User login/logout
- [ ] Customer creation and editing
- [ ] Supplier management
- [ ] Inventory tracking
- [ ] Invoice generation
- [ ] Payment recording
- [ ] Report generation

#### Test Python Integration
- [ ] Database connection
- [ ] Analytics functions
- [ ] Email notifications
- [ ] Automated reports

### 2. Performance Optimization

#### FileMaker Optimization
- Configure memory allocation
- Set up indexing for key fields
- Optimize layouts for performance
- Configure caching settings

#### Python Optimization
- Set up task scheduling
- Configure logging
- Optimize query performance
- Set up error handling

### 3. Security Configuration

#### Database Security
- Review user permissions
- Configure privilege sets
- Set up audit logging
- Enable encryption

#### System Security
- Configure Windows/macOS security
- Set up antivirus exclusions
- Configure backup encryption
- Review network security

### 4. Backup Configuration

#### Automatic Backups
- Configure FileMaker Server backups
- Set up file system backups
- Test restore procedures
- Document backup locations

#### Backup Schedule
- **Hourly**: Transaction log backups
- **Daily**: Full database backup
- **Weekly**: System image backup
- **Monthly**: Offsite backup verification

## Training and Documentation

### 1. User Training

#### Initial Training Session
- System overview
- Basic navigation
- Role-specific functions
- Security best practices

#### Ongoing Training
- Monthly user meetings
- Feature updates
- Best practices sharing
- Troubleshooting sessions

### 2. Documentation

#### System Documentation
- Keep installation guide updated
- Maintain user manual
- Document customizations
- Record configuration changes

#### Operational Procedures
- Daily operation checklists
- Backup procedures
- Troubleshooting guides
- Emergency contacts

## Troubleshooting Installation Issues

### Common Problems

#### FileMaker Installation Issues
**Problem**: License key not accepted
- **Solution**: Verify key format and contact Claris support

**Problem**: Database won't open
- **Solution**: Check file permissions and FileMaker version compatibility

#### Python Integration Issues
**Problem**: Module import errors
- **Solution**: Verify virtual environment activation and package installation

**Problem**: Database connection fails
- **Solution**: Check ODBC driver installation and connection settings

#### Network Issues
**Problem**: Cannot connect to FileMaker Server
- **Solution**: Verify network connectivity and firewall settings

**Problem**: Slow performance over network
- **Solution**: Check network bandwidth and optimize layouts

### Getting Help

#### Technical Support Contacts
- **FileMaker Support**: Claris technical support
- **System Administrator**: [Your IT contact]
- **Vendor Support**: [Implementation partner]

#### Documentation Resources
- FileMaker Pro documentation
- Python integration guides
- System user manual
- Online knowledge base

## Maintenance Schedule

### Daily Tasks
- [ ] Verify system operation
- [ ] Check backup completion
- [ ] Monitor user activity
- [ ] Review error logs

### Weekly Tasks
- [ ] Performance monitoring
- [ ] Security updates
- [ ] User account review
- [ ] Documentation updates

### Monthly Tasks
- [ ] Full system backup test
- [ ] Security audit
- [ ] Performance optimization
- [ ] User training updates

### Quarterly Tasks
- [ ] System upgrade planning
- [ ] Disaster recovery testing
- [ ] License compliance review
- [ ] Capacity planning

## Conclusion

The Al-Hassan Stone Factory Management System is now installed and configured. Regular maintenance and monitoring will ensure optimal performance and security.

For ongoing support and questions, refer to the user manual or contact your system administrator.

---

**Installation Guide Version**: 1.0  
**Last Updated**: January 2024  
**Compatible with**: FileMaker Pro 19+, Python 3.8+
