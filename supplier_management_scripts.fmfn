# Al-Hassan Stone Factory - Supplier Management Scripts
# FileMaker Pro Custom Functions and Scripts

## Custom Functions

### Calculate Supplier Outstanding Balance
/**
 * Function: SupplierOutstandingBalance
 * Purpose: Calculate the outstanding balance for a supplier
 * Parameters: supplierID (Text)
 * Returns: Number
 */
Let([
    totalPurchases = Sum(PURCHASE_ORDERS::total_amount);
    totalPayments = Sum(SUPPLIER_PAYMENTS::amount);
    outstanding = totalPurchases - totalPayments
];
    outstanding
)

### Format Supplier ID
/**
 * Function: FormatSupplierID
 * Purpose: Generate formatted supplier ID
 * Parameters: serialNumber (Number)
 * Returns: Text
 */
"SUPP" & Right("0000" & serialNumber; 4)

### Calculate Supplier Performance Score
/**
 * Function: SupplierPerformanceScore
 * Purpose: Calculate supplier performance based on delivery and quality
 * Parameters: supplierID (Text)
 * Returns: Number (0-100)
 */
Let([
    totalOrders = Count(PURCHASE_ORDERS::po_id);
    onTimeDeliveries = Count(PURCHASE_ORDERS::po_id; PURCHASE_ORDERS::status = "Received");
    qualityScore = 85; // Base quality score, can be enhanced with quality tracking
    deliveryScore = If(totalOrders > 0; (onTimeDeliveries / totalOrders) * 100; 0);
    performanceScore = (deliveryScore * 0.6) + (qualityScore * 0.4)
];
    Round(performanceScore; 1)
)

### Get Supplier Total Purchases
/**
 * Function: SupplierTotalPurchases
 * Purpose: Get total purchase amount for a supplier in a date range
 * Parameters: supplierID (Text), startDate (Date), endDate (Date)
 * Returns: Number
 */
Let([
    filteredPOs = Filter(PURCHASE_ORDERS::po_id; 
        PURCHASE_ORDERS::order_date >= startDate and 
        PURCHASE_ORDERS::order_date <= endDate);
    totalAmount = Sum(PURCHASE_ORDERS::total_amount)
];
    totalAmount
)

## Scripts

### Script: New Supplier
/**
 * Purpose: Create a new supplier record with validation
 */
# Set error capture on
Set Error Capture [On]

# Create new supplier record
New Record/Request

# Set default values
Set Field [SUPPLIERS::supplier_id; FormatSupplierID(Get(RecordNumber))]
Set Field [SUPPLIERS::created_date; Get(CurrentTimeStamp)]
Set Field [SUPPLIERS::modified_date; Get(CurrentTimeStamp)]
Set Field [SUPPLIERS::active_status; 1]
Set Field [SUPPLIERS::supplier_type; "Raw Granite"]
Set Field [SUPPLIERS::payment_terms; "Net 30"]

# Go to supplier detail layout
Go to Layout ["Supplier_Detail"]

# Set focus to name field
Go to Field [SUPPLIERS::supplier_name]

### Script: Save Supplier
/**
 * Purpose: Save supplier record with validation
 */
# Set error capture on
Set Error Capture [On]

# Validate required fields
If [IsEmpty(SUPPLIERS::supplier_name)]
    Show Custom Dialog ["Error"; "Supplier name is required."]
    Go to Field [SUPPLIERS::supplier_name]
    Exit Script [Text Result: "Error"]
End If

If [IsEmpty(SUPPLIERS::supplier_type)]
    Show Custom Dialog ["Error"; "Supplier type is required."]
    Go to Field [SUPPLIERS::supplier_type]
    Exit Script [Text Result: "Error"]
End If

If [IsEmpty(SUPPLIERS::phone_number)]
    Show Custom Dialog ["Error"; "Phone number is required."]
    Go to Field [SUPPLIERS::phone_number]
    Exit Script [Text Result: "Error"]
End If

# Validate email if provided
If [not IsEmpty(SUPPLIERS::email) and not IsValidEmail(SUPPLIERS::email)]
    Show Custom Dialog ["Error"; "Please enter a valid email address."]
    Go to Field [SUPPLIERS::email]
    Exit Script [Text Result: "Error"]
End If

# Update modified date
Set Field [SUPPLIERS::modified_date; Get(CurrentTimeStamp)]

# Commit record
Commit Records/Requests [With dialog: Off]

# Show success message
Show Custom Dialog ["Success"; "Supplier saved successfully."]

### Script: Search Suppliers
/**
 * Purpose: Search suppliers by various criteria
 */
# Set error capture on
Set Error Capture [On]

# Go to supplier list layout
Go to Layout ["Supplier_List"]

# Enter find mode
Enter Find Mode [Pause: Off]

# Set search criteria based on global fields
If [not IsEmpty(Globals::g_search_supplier_name)]
    Set Field [SUPPLIERS::supplier_name; "*" & Globals::g_search_supplier_name & "*"]
End If

If [not IsEmpty(Globals::g_search_supplier_type)]
    Set Field [SUPPLIERS::supplier_type; Globals::g_search_supplier_type]
End If

If [not IsEmpty(Globals::g_search_contact)]
    Set Field [SUPPLIERS::contact_person; "*" & Globals::g_search_contact & "*"]
End If

# Perform find
Perform Find [Restore]

# Sort results by name
Sort Records [Restore; With dialog: Off]
    [SUPPLIERS::supplier_name; ascending]

### Script: Create Purchase Order
/**
 * Purpose: Create a new purchase order for selected supplier
 */
# Set error capture on
Set Error Capture [On]

# Validate supplier selection
If [IsEmpty(SUPPLIERS::supplier_id)]
    Show Custom Dialog ["Error"; "Please select a supplier first."]
    Exit Script [Text Result: "Error"]
End If

# Go to purchase order layout
Go to Layout ["PO_Detail"]

# Create new PO record
New Record/Request

# Set default values
Set Field [PURCHASE_ORDERS::supplier_id; SUPPLIERS::supplier_id]
Set Field [PURCHASE_ORDERS::po_number; "PO" & SerialIncrement(POSerial, 1)]
Set Field [PURCHASE_ORDERS::order_date; Get(CurrentDate)]
Set Field [PURCHASE_ORDERS::status; "Pending"]
Set Field [PURCHASE_ORDERS::currency; "EGP"]
Set Field [PURCHASE_ORDERS::created_date; Get(CurrentTimeStamp)]
Set Field [PURCHASE_ORDERS::modified_date; Get(CurrentTimeStamp)]

# Set expected delivery (default 7 days from order)
Set Field [PURCHASE_ORDERS::expected_delivery; Get(CurrentDate) + 7]

# Go to PO number field
Go to Field [PURCHASE_ORDERS::po_number]

### Script: Add PO Item
/**
 * Purpose: Add item to current purchase order
 */
# Set error capture on
Set Error Capture [On]

# Validate PO exists
If [IsEmpty(PURCHASE_ORDERS::po_id)]
    Show Custom Dialog ["Error"; "Please create or select a purchase order first."]
    Exit Script [Text Result: "Error"]
End If

# Go to PO items portal
Go to Portal Row [PURCHASE_ORDER_ITEMS; Last]

# Create new item record
New Record/Request

# Set default values
Set Field [PURCHASE_ORDER_ITEMS::po_id; PURCHASE_ORDERS::po_id]
Set Field [PURCHASE_ORDER_ITEMS::quantity; 1]
Set Field [PURCHASE_ORDER_ITEMS::unit; "cubic meter"]

# Go to item description field
Go to Field [PURCHASE_ORDER_ITEMS::item_description]

### Script: Calculate PO Total
/**
 * Purpose: Calculate total amount for purchase order
 */
# Set error capture on
Set Error Capture [On]

# Calculate total from all items
Set Variable [$total; Value: Sum(PURCHASE_ORDER_ITEMS::total_price)]

# Update PO total
Set Field [PURCHASE_ORDERS::total_amount; $total]
Set Field [PURCHASE_ORDERS::modified_date; Get(CurrentTimeStamp)]

# Commit record
Commit Records/Requests [With dialog: Off]

### Script: Receive PO Items
/**
 * Purpose: Mark purchase order as received and create block records
 */
# Set error capture on
Set Error Capture [On]

# Validate PO selection
If [IsEmpty(PURCHASE_ORDERS::po_id)]
    Show Custom Dialog ["Error"; "Please select a purchase order first."]
    Exit Script [Text Result: "Error"]
End If

# Check if PO is already received
If [PURCHASE_ORDERS::status = "Received"]
    Show Custom Dialog ["Error"; "This purchase order has already been received."]
    Exit Script [Text Result: "Error"]
End If

# Confirm receipt
Show Custom Dialog ["Confirm Receipt"; 
    "Mark PO " & PURCHASE_ORDERS::po_number & " as received?"]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Update PO status
Set Field [PURCHASE_ORDERS::status; "Received"]
Set Field [PURCHASE_ORDERS::modified_date; Get(CurrentTimeStamp)]

# Create block records for granite items
Go to Portal Row [PURCHASE_ORDER_ITEMS; First]
Loop
    # Check if item is granite/stone
    If [PatternCount(Lower(PURCHASE_ORDER_ITEMS::item_description); "granite") > 0 or
        PatternCount(Lower(PURCHASE_ORDER_ITEMS::item_description); "stone") > 0 or
        PatternCount(Lower(PURCHASE_ORDER_ITEMS::item_description); "marble") > 0]
        
        # Create block record
        Go to Layout ["Block_Detail"]
        New Record/Request
        
        # Set block fields
        Set Field [BLOCKS::supplier_id; PURCHASE_ORDERS::supplier_id]
        Set Field [BLOCKS::po_id; PURCHASE_ORDERS::po_id]
        Set Field [BLOCKS::block_type; PURCHASE_ORDER_ITEMS::item_description]
        Set Field [BLOCKS::received_date; Get(CurrentDate)]
        Set Field [BLOCKS::status; "Available"]
        Set Field [BLOCKS::location; "Warehouse A"]
        
        # Commit block record
        Commit Records/Requests [With dialog: Off]
        
        # Return to PO layout
        Go to Layout ["PO_Detail"]
    End If
    
    # Go to next item
    Go to Portal Row [PURCHASE_ORDER_ITEMS; Next]
    Exit Loop If [Get(LastError) = 101] # No more records
End Loop

# Commit PO record
Commit Records/Requests [With dialog: Off]

# Show success message
Show Custom Dialog ["Success"; "Purchase order marked as received. Block records created for granite items."]

### Script: Supplier Payment Entry
/**
 * Purpose: Record a payment to supplier
 */
# Set error capture on
Set Error Capture [On]

# Validate supplier selection
If [IsEmpty(SUPPLIERS::supplier_id)]
    Show Custom Dialog ["Error"; "Please select a supplier first."]
    Exit Script [Text Result: "Error"]
End If

# Go to supplier payment layout
Go to Layout ["Supplier_Payment"]

# Create new payment record
New Record/Request

# Set default values
Set Field [SUPPLIER_PAYMENTS::supplier_id; SUPPLIERS::supplier_id]
Set Field [SUPPLIER_PAYMENTS::payment_date; Get(CurrentDate)]
Set Field [SUPPLIER_PAYMENTS::currency; "EGP"]
Set Field [SUPPLIER_PAYMENTS::payment_method; "Bank Transfer"]
Set Field [SUPPLIER_PAYMENTS::created_date; Get(CurrentTimeStamp)]

# Show payment dialog
Show Custom Dialog ["Supplier Payment"; "Enter payment details:";
    "Amount:"; SUPPLIER_PAYMENTS::amount;
    "Payment Method:"; SUPPLIER_PAYMENTS::payment_method;
    "Reference:"; SUPPLIER_PAYMENTS::reference_number]

If [Get(LastMessageChoice) = 2]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Cancelled"]
End If

# Validate payment amount
If [SUPPLIER_PAYMENTS::amount <= 0]
    Show Custom Dialog ["Error"; "Payment amount must be greater than zero."]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Error"]
End If

# Commit record
Commit Records/Requests [With dialog: Off]

# Update supplier's outstanding balance
Go to Layout ["Supplier_Detail"]
Refresh Window

# Show success message
Show Custom Dialog ["Success"; "Payment recorded successfully."]

### Script: Generate Supplier Report
/**
 * Purpose: Generate supplier performance and cost analysis report
 */
# Set error capture on
Set Error Capture [On]

# Validate supplier selection
If [IsEmpty(SUPPLIERS::supplier_id)]
    Show Custom Dialog ["Error"; "Please select a supplier first."]
    Exit Script [Text Result: "Error"]
End If

# Set date range (default to current year)
Set Variable [$startDate; Value: Date(1; 1; Year(Get(CurrentDate)))]
Set Variable [$endDate; Value: Get(CurrentDate)]

# Show date range dialog
Show Custom Dialog ["Report Period"; "Select report period:";
    "From:"; Globals::g_date_from;
    "To:"; Globals::g_date_to]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set date variables
Set Variable [$startDate; Value: Globals::g_date_from]
Set Variable [$endDate; Value: Globals::g_date_to]

# Create new supplier report record
Go to Layout ["Supplier_Reports"]
New Record/Request

# Set report fields
Set Field [SUPPLIER_REPORTS::supplier_id; SUPPLIERS::supplier_id]
Set Field [SUPPLIER_REPORTS::report_type; "Performance Analysis"]
Set Field [SUPPLIER_REPORTS::report_date; Get(CurrentDate)]
Set Field [SUPPLIER_REPORTS::created_date; Get(CurrentTimeStamp)]

# Calculate report data
Set Variable [$totalPurchases; Value: SupplierTotalPurchases(SUPPLIERS::supplier_id; $startDate; $endDate)]
Set Variable [$totalPayments; Value: Sum(SUPPLIER_PAYMENTS::amount)]
Set Variable [$outstandingBalance; Value: $totalPurchases - $totalPayments]
Set Variable [$performanceScore; Value: SupplierPerformanceScore(SUPPLIERS::supplier_id)]

# Set calculated fields
Set Field [SUPPLIER_REPORTS::total_purchases; $totalPurchases]
Set Field [SUPPLIER_REPORTS::total_payments; $totalPayments]
Set Field [SUPPLIER_REPORTS::outstanding_balance; $outstandingBalance]

# Generate report data JSON
Set Variable [$reportData; Value: 
    "{" &
    "\"supplier_id\":\"" & SUPPLIERS::supplier_id & "\"," &
    "\"supplier_name\":\"" & SUPPLIERS::supplier_name & "\"," &
    "\"report_period\":{" &
        "\"from\":\"" & $startDate & "\"," &
        "\"to\":\"" & $endDate & "\"" &
    "}," &
    "\"total_purchases\":" & $totalPurchases & "," &
    "\"total_payments\":" & $totalPayments & "," &
    "\"outstanding_balance\":" & $outstandingBalance & "," &
    "\"performance_score\":" & $performanceScore &
    "}"
]

Set Field [SUPPLIER_REPORTS::report_data; $reportData]

# Commit record
Commit Records/Requests [With dialog: Off]

# Go to report layout and print
Go to Layout ["Supplier_Report"]
Print Setup [Restore; With dialog: Off]
Print [With dialog: On]

### Script: Delete Supplier
/**
 * Purpose: Delete supplier with validation
 */
# Set error capture on
Set Error Capture [On]

# Validate supplier selection
If [IsEmpty(SUPPLIERS::supplier_id)]
    Show Custom Dialog ["Error"; "Please select a supplier first."]
    Exit Script [Text Result: "Error"]
End If

# Check for related records
Set Variable [$poCount; Value: Count(PURCHASE_ORDERS::po_id)]
Set Variable [$paymentCount; Value: Count(SUPPLIER_PAYMENTS::supplier_payment_id)]
Set Variable [$blockCount; Value: Count(BLOCKS::block_id)]
Set Variable [$expenseCount; Value: Count(EXPENSES::expense_id)]

If [$poCount > 0 or $paymentCount > 0 or $blockCount > 0 or $expenseCount > 0]
    Show Custom Dialog ["Cannot Delete"; 
        "This supplier has related transactions and cannot be deleted. " &
        "You can deactivate the supplier instead."]
    Exit Script [Text Result: "Error"]
End If

# Confirm deletion
Show Custom Dialog ["Confirm Delete"; 
    "Are you sure you want to delete supplier: " & SUPPLIERS::supplier_name & "?";
    "This action cannot be undone."]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Delete supplier
Delete Record/Request [With dialog: Off]

# Go to supplier list
Go to Layout ["Supplier_List"]

# Show success message
Show Custom Dialog ["Success"; "Supplier deleted successfully."]

### Script: Supplier Performance Dashboard
/**
 * Purpose: Show supplier performance metrics
 */
# Set error capture on
Set Error Capture [On]

# Go to supplier list layout
Go to Layout ["Supplier_List"]

# Show all active suppliers
Enter Find Mode [Pause: Off]
Set Field [SUPPLIERS::active_status; 1]
Perform Find [Restore]

# Sort by performance score (descending)
Sort Records [Restore; With dialog: Off]
    [SupplierPerformanceScore(SUPPLIERS::supplier_id); descending]

# Go to performance dashboard layout
Go to Layout ["Supplier_Performance_Dashboard"]

# Refresh calculations
Refresh Window

### Script: Export Supplier Data
/**
 * Purpose: Export supplier data to Excel
 */
# Set error capture on
Set Error Capture [On]

# Go to supplier list layout
Go to Layout ["Supplier_List"]

# Show all records
Show All Records

# Sort by supplier name
Sort Records [Restore; With dialog: Off]
    [SUPPLIERS::supplier_name; ascending]

# Export records
Export Records [With dialog: On; "Supplier_Export.xlsx"]

# Show completion message
Show Custom Dialog ["Export Complete"; "Supplier data exported successfully."]

### Script: Supplier Cost Analysis
/**
 * Purpose: Analyze supplier costs and trends
 */
# Set error capture on
Set Error Capture [On]

# Set analysis period (default to last 12 months)
Set Variable [$startDate; Value: Get(CurrentDate) - 365]
Set Variable [$endDate; Value: Get(CurrentDate)]

# Show date range dialog
Show Custom Dialog ["Cost Analysis Period"; "Select analysis period:";
    "From:"; Globals::g_date_from;
    "To:"; Globals::g_date_to]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Go to supplier list layout
Go to Layout ["Supplier_List"]

# Find suppliers with purchases in period
Enter Find Mode [Pause: Off]
Set Field [PURCHASE_ORDERS::order_date; Globals::g_date_from & ".." & Globals::g_date_to]
Perform Find [Restore]

# Sort by total purchase amount (descending)
Sort Records [Restore; With dialog: Off]
    [SupplierTotalPurchases(SUPPLIERS::supplier_id; Globals::g_date_from; Globals::g_date_to); descending]

# Go to cost analysis layout
Go to Layout ["Supplier_Cost_Analysis"]

# Print report
Print Setup [Restore; With dialog: Off]
Print [With dialog: On]
