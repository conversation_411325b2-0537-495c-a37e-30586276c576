# Al-Hassan Stone Factory - Customer Management Scripts
# FileMaker Pro Custom Functions and Scripts

## Custom Functions

### Calculate Customer Outstanding Balance
/**
 * Function: CustomerOutstandingBalance
 * Purpose: Calculate the outstanding balance for a customer
 * Parameters: customerID (Text)
 * Returns: Number
 */
Let([
    totalInvoices = Sum(INVOICES::net_amount);
    totalPayments = Sum(PAYMENTS::amount);
    outstanding = totalInvoices - totalPayments
];
    outstanding
)

### Format Customer ID
/**
 * Function: FormatCustomerID
 * Purpose: Generate formatted customer ID
 * Parameters: serialNumber (Number)
 * Returns: Text
 */
"CUST" & Right("0000" & serialNumber; 4)

### Validate Phone Number
/**
 * Function: ValidatePhoneNumber
 * Purpose: Validate Egyptian phone number format
 * Parameters: phoneNumber (Text)
 * Returns: Boolean
 */
Let([
    cleanPhone = Substitute(phoneNumber; [" "; ""]; ["-"; ""]; ["("; ""]; [")"; ""]];
    length = Length(cleanPhone);
    startsWithCountryCode = Left(cleanPhone; 3) = "+20" or Left(cleanPhone; 2) = "20";
    startsWithZero = Left(cleanPhone; 1) = "0";
    isNumeric = IsValid(GetAsNumber(cleanPhone))
];
    Case(
        startsWithCountryCode and length = 13; 1;
        startsWithZero and length = 11; 1;
        length = 10; 1;
        0
    )
)

### Calculate Customer Profit
/**
 * Function: CustomerProfit
 * Purpose: Calculate profit from a specific customer
 * Parameters: customerID (Text)
 * Returns: Number
 */
Let([
    totalRevenue = Sum(INVOICES::net_amount);
    totalCosts = Sum(CUTS::total_cost);
    profit = totalRevenue - totalCosts
];
    profit
)

## Scripts

### Script: New Customer
/**
 * Purpose: Create a new customer record with validation
 */
# Set error capture on
Set Error Capture [On]

# Create new customer record
New Record/Request

# Set default values
Set Field [CUSTOMERS::customer_id; FormatCustomerID(Get(RecordNumber))]
Set Field [CUSTOMERS::created_date; Get(CurrentTimeStamp)]
Set Field [CUSTOMERS::modified_date; Get(CurrentTimeStamp)]
Set Field [CUSTOMERS::active_status; 1]
Set Field [CUSTOMERS::currency; "EGP"]
Set Field [CUSTOMERS::preferred_payment_method; "Cash"]

# Go to customer detail layout
Go to Layout ["Customer_Detail"]

# Set focus to name field
Go to Field [CUSTOMERS::full_name]

### Script: Save Customer
/**
 * Purpose: Save customer record with validation
 */
# Set error capture on
Set Error Capture [On]

# Validate required fields
If [IsEmpty(CUSTOMERS::full_name)]
    Show Custom Dialog ["Error"; "Customer name is required."]
    Go to Field [CUSTOMERS::full_name]
    Exit Script [Text Result: "Error"]
End If

If [IsEmpty(CUSTOMERS::phone_number)]
    Show Custom Dialog ["Error"; "Phone number is required."]
    Go to Field [CUSTOMERS::phone_number]
    Exit Script [Text Result: "Error"]
End If

# Validate phone number format
If [not ValidatePhoneNumber(CUSTOMERS::phone_number)]
    Show Custom Dialog ["Error"; "Please enter a valid phone number."]
    Go to Field [CUSTOMERS::phone_number]
    Exit Script [Text Result: "Error"]
End If

# Validate email if provided
If [not IsEmpty(CUSTOMERS::email) and not IsValidEmail(CUSTOMERS::email)]
    Show Custom Dialog ["Error"; "Please enter a valid email address."]
    Go to Field [CUSTOMERS::email]
    Exit Script [Text Result: "Error"]
End If

# Update modified date
Set Field [CUSTOMERS::modified_date; Get(CurrentTimeStamp)]

# Commit record
Commit Records/Requests [With dialog: Off]

# Show success message
Show Custom Dialog ["Success"; "Customer saved successfully."]

### Script: Search Customers
/**
 * Purpose: Search customers by various criteria
 */
# Set error capture on
Set Error Capture [On]

# Go to customer list layout
Go to Layout ["Customer_List"]

# Enter find mode
Enter Find Mode [Pause: Off]

# Set search criteria based on global fields
If [not IsEmpty(Globals::g_search_name)]
    Set Field [CUSTOMERS::full_name; "*" & Globals::g_search_name & "*"]
End If

If [not IsEmpty(Globals::g_search_phone)]
    Set Field [CUSTOMERS::phone_number; "*" & Globals::g_search_phone & "*"]
End If

If [not IsEmpty(Globals::g_search_company)]
    Set Field [CUSTOMERS::company_contractor_name; "*" & Globals::g_search_company & "*"]
End If

# Perform find
Perform Find [Restore]

# Sort results by name
Sort Records [Restore; With dialog: Off]
    [CUSTOMERS::full_name; ascending]

### Script: Generate Customer Statement
/**
 * Purpose: Generate customer account statement
 */
# Set error capture on
Set Error Capture [On]

# Validate customer selection
If [IsEmpty(CUSTOMERS::customer_id)]
    Show Custom Dialog ["Error"; "Please select a customer first."]
    Exit Script [Text Result: "Error"]
End If

# Set date range (default to current month)
Set Variable [$startDate; Value: Date(Month(Get(CurrentDate)); 1; Year(Get(CurrentDate)))]
Set Variable [$endDate; Value: Get(CurrentDate)]

# Show date range dialog
Show Custom Dialog ["Date Range"; "Select statement period:"; 
    "From:"; Globals::g_date_from; 
    "To:"; Globals::g_date_to]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Set date variables
Set Variable [$startDate; Value: Globals::g_date_from]
Set Variable [$endDate; Value: Globals::g_date_to]

# Create new customer report record
Go to Layout ["Customer_Reports"]
New Record/Request

# Set report fields
Set Field [CUSTOMER_REPORTS::customer_id; CUSTOMERS::customer_id]
Set Field [CUSTOMER_REPORTS::report_type; "Account Statement"]
Set Field [CUSTOMER_REPORTS::report_date; Get(CurrentDate)]
Set Field [CUSTOMER_REPORTS::created_date; Get(CurrentTimeStamp)]

# Calculate statement data
Set Variable [$openingBalance; Value: CustomerOutstandingBalance(CUSTOMERS::customer_id)]
Set Variable [$totalSales; Value: Sum(INVOICES::net_amount)]
Set Variable [$totalPayments; Value: Sum(PAYMENTS::amount)]
Set Variable [$closingBalance; Value: $openingBalance]

# Set calculated fields
Set Field [CUSTOMER_REPORTS::total_sales; $totalSales]
Set Field [CUSTOMER_REPORTS::total_payments; $totalPayments]
Set Field [CUSTOMER_REPORTS::outstanding_balance; $closingBalance]

# Generate report data JSON
Set Variable [$reportData; Value: 
    "{" &
    "\"customer_id\":\"" & CUSTOMERS::customer_id & "\"," &
    "\"customer_name\":\"" & CUSTOMERS::full_name & "\"," &
    "\"statement_period\":{" &
        "\"from\":\"" & $startDate & "\"," &
        "\"to\":\"" & $endDate & "\"" &
    "}," &
    "\"opening_balance\":" & $openingBalance & "," &
    "\"total_sales\":" & $totalSales & "," &
    "\"total_payments\":" & $totalPayments & "," &
    "\"closing_balance\":" & $closingBalance &
    "}"
]

Set Field [CUSTOMER_REPORTS::report_data; $reportData]

# Commit record
Commit Records/Requests [With dialog: Off]

# Go to statement layout and print
Go to Layout ["Customer_Statement"]
Print Setup [Restore; With dialog: Off]
Print [With dialog: On]

### Script: Customer Payment Entry
/**
 * Purpose: Record a payment from customer
 */
# Set error capture on
Set Error Capture [On]

# Validate customer selection
If [IsEmpty(CUSTOMERS::customer_id)]
    Show Custom Dialog ["Error"; "Please select a customer first."]
    Exit Script [Text Result: "Error"]
End If

# Go to payment entry layout
Go to Layout ["Payment_Entry"]

# Create new payment record
New Record/Request

# Set default values
Set Field [PAYMENTS::customer_id; CUSTOMERS::customer_id]
Set Field [PAYMENTS::payment_date; Get(CurrentDate)]
Set Field [PAYMENTS::currency; CUSTOMERS::currency]
Set Field [PAYMENTS::payment_method; CUSTOMERS::preferred_payment_method]
Set Field [PAYMENTS::created_date; Get(CurrentTimeStamp)]

# Show payment dialog
Show Custom Dialog ["Payment Entry"; "Enter payment details:";
    "Amount:"; PAYMENTS::amount;
    "Payment Method:"; PAYMENTS::payment_method;
    "Reference:"; PAYMENTS::reference_number]

If [Get(LastMessageChoice) = 2]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Cancelled"]
End If

# Validate payment amount
If [PAYMENTS::amount <= 0]
    Show Custom Dialog ["Error"; "Payment amount must be greater than zero."]
    Delete Record/Request [With dialog: Off]
    Exit Script [Text Result: "Error"]
End If

# Update modified date
Set Field [PAYMENTS::modified_date; Get(CurrentTimeStamp)]

# Commit record
Commit Records/Requests [With dialog: Off]

# Update customer's outstanding balance
Go to Layout ["Customer_Detail"]
Refresh Window

# Show success message
Show Custom Dialog ["Success"; "Payment recorded successfully."]

### Script: Delete Customer
/**
 * Purpose: Delete customer with validation
 */
# Set error capture on
Set Error Capture [On]

# Validate customer selection
If [IsEmpty(CUSTOMERS::customer_id)]
    Show Custom Dialog ["Error"; "Please select a customer first."]
    Exit Script [Text Result: "Error"]
End If

# Check for related records
Set Variable [$invoiceCount; Value: Count(INVOICES::invoice_id)]
Set Variable [$paymentCount; Value: Count(PAYMENTS::payment_id)]
Set Variable [$cutCount; Value: Count(CUTS::cut_id)]

If [$invoiceCount > 0 or $paymentCount > 0 or $cutCount > 0]
    Show Custom Dialog ["Cannot Delete"; 
        "This customer has related transactions and cannot be deleted. " &
        "You can deactivate the customer instead."]
    Exit Script [Text Result: "Error"]
End If

# Confirm deletion
Show Custom Dialog ["Confirm Delete"; 
    "Are you sure you want to delete customer: " & CUSTOMERS::full_name & "?";
    "This action cannot be undone."]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Delete customer
Delete Record/Request [With dialog: Off]

# Go to customer list
Go to Layout ["Customer_List"]

# Show success message
Show Custom Dialog ["Success"; "Customer deleted successfully."]

### Script: Export Customer Data
/**
 * Purpose: Export customer data to Excel
 */
# Set error capture on
Set Error Capture [On]

# Go to customer list layout
Go to Layout ["Customer_List"]

# Show all records
Show All Records

# Sort by customer name
Sort Records [Restore; With dialog: Off]
    [CUSTOMERS::full_name; ascending]

# Export records
Export Records [With dialog: On; "Customer_Export.xlsx"]

# Show completion message
Show Custom Dialog ["Export Complete"; "Customer data exported successfully."]

### Script: Import Customer Data
/**
 * Purpose: Import customer data from Excel
 */
# Set error capture on
Set Error Capture [On]

# Show import dialog
Show Custom Dialog ["Import Customers"; 
    "Select Excel file to import customer data."]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Go to customer list layout
Go to Layout ["Customer_List"]

# Import records
Import Records [With dialog: On; "Customer_Import.xlsx"]

# Show completion message
Show Custom Dialog ["Import Complete"; 
    "Customer data imported successfully. " &
    "Please review the imported records."]

### Script: Customer Activity Report
/**
 * Purpose: Generate customer activity report
 */
# Set error capture on
Set Error Capture [On]

# Set date range
Set Variable [$startDate; Value: Date(Month(Get(CurrentDate)); 1; Year(Get(CurrentDate)))]
Set Variable [$endDate; Value: Get(CurrentDate)]

# Show date range dialog
Show Custom Dialog ["Activity Report"; "Select report period:";
    "From:"; Globals::g_date_from;
    "To:"; Globals::g_date_to]

If [Get(LastMessageChoice) = 2]
    Exit Script [Text Result: "Cancelled"]
End If

# Go to customer list layout
Go to Layout ["Customer_List"]

# Find customers with activity in date range
Enter Find Mode [Pause: Off]
Set Field [INVOICES::invoice_date; Globals::g_date_from & ".." & Globals::g_date_to]
Perform Find [Restore]

# Sort by total sales (descending)
Sort Records [Restore; With dialog: Off]
    [CustomerOutstandingBalance(CUSTOMERS::customer_id); descending]

# Go to report layout
Go to Layout ["Customer_Activity_Report"]

# Print report
Print Setup [Restore; With dialog: Off]
Print [With dialog: On]
