# Al-Hassan Stone Factory - Database Schema Documentation

## Overview
This document outlines the comprehensive database schema for the Al-Hassan Stone factory management system, designed for FileMaker Pro with Python integration.

## Core Tables

### 1. CUSTOMERS
**Purpose**: Store customer information and profiles
- `customer_id` (Text, Primary Key): Unique identifier (e.g., CUST001)
- `full_name` (Text, Required): Customer's full name
- `phone_number` (Text, Required): Primary contact number
- `address` (Text): Customer's address
- `email` (Text): Email address (optional)
- `company_contractor_name` (Text): Company or contractor name
- `preferred_payment_method` (Text): Cash/Credit/Installments
- `currency` (Text): EGP/USD/Other
- `notes` (Text): Additional notes
- `created_date` (Timestamp): Record creation date
- `modified_date` (Timestamp): Last modification date
- `active_status` (Boolean): Active/Inactive status

### 2. SUPPLIERS
**Purpose**: Store supplier information and profiles
- `supplier_id` (Text, Primary Key): Unique identifier (e.g., SUPP001)
- `supplier_name` (Text, Required): Supplier/company name
- `supplier_type` (Text): Raw granite/Transportation/Machinery/Maintenance/Other
- `contact_person` (Text): Primary contact person
- `phone_number` (Text, Required): Contact number
- `email` (Text): Email address
- `address` (Text): Supplier's address
- `payment_terms` (Text): Agreed payment terms
- `products_services` (Text): Products/services provided
- `created_date` (Timestamp): Record creation date
- `modified_date` (Timestamp): Last modification date
- `active_status` (Boolean): Active/Inactive status

### 3. INVOICES
**Purpose**: Store sales invoices to customers
- `invoice_id` (Text, Primary Key): Unique identifier (e.g., INV001)
- `customer_id` (Text, Foreign Key): Link to CUSTOMERS table
- `invoice_number` (Text, Required): Sequential invoice number
- `invoice_date` (Date, Required): Invoice date
- `total_amount` (Number): Total invoice amount before tax/discount
- `tax_amount` (Number): Tax amount
- `discount_amount` (Number): Discount amount
- `net_amount` (Number): Final amount after tax/discount
- `currency` (Text): Invoice currency
- `status` (Text): Draft/Sent/Paid/Overdue/Cancelled
- `due_date` (Date): Payment due date
- `notes` (Text): Additional notes
- `created_date` (Timestamp): Record creation date
- `modified_date` (Timestamp): Last modification date

### 4. INVOICE_ITEMS
**Purpose**: Store individual items within invoices
- `item_id` (Text, Primary Key): Unique identifier
- `invoice_id` (Text, Foreign Key): Link to INVOICES table
- `product_description` (Text, Required): Description of product/service
- `quantity` (Number, Required): Quantity sold
- `unit` (Text): Unit of measurement (sqm, pieces, etc.)
- `unit_price` (Number, Required): Price per unit
- `total_price` (Number, Calculated): quantity × unit_price
- `cut_id` (Text, Foreign Key): Link to CUTS table (if applicable)
- `notes` (Text): Additional notes

### 5. PAYMENTS
**Purpose**: Track customer payments and receivables
- `payment_id` (Text, Primary Key): Unique identifier
- `customer_id` (Text, Foreign Key): Link to CUSTOMERS table
- `invoice_id` (Text, Foreign Key): Link to INVOICES table (optional)
- `amount` (Number, Required): Payment amount
- `payment_method` (Text): Cash/Bank Transfer/Check/Credit Card
- `currency` (Text): Payment currency
- `payment_date` (Date, Required): Date of payment
- `reference_number` (Text): Bank reference or check number
- `notes` (Text): Additional notes
- `created_date` (Timestamp): Record creation date
- `modified_date` (Timestamp): Last modification date

### 6. PURCHASE_ORDERS
**Purpose**: Track purchases from suppliers
- `po_id` (Text, Primary Key): Unique identifier (e.g., PO001)
- `supplier_id` (Text, Foreign Key): Link to SUPPLIERS table
- `po_number` (Text, Required): Purchase order number
- `order_date` (Date, Required): Order date
- `total_amount` (Number): Total order amount
- `currency` (Text): Order currency
- `status` (Text): Pending/Received/Cancelled
- `expected_delivery` (Date): Expected delivery date
- `notes` (Text): Additional notes
- `created_date` (Timestamp): Record creation date
- `modified_date` (Timestamp): Last modification date

### 7. PURCHASE_ORDER_ITEMS
**Purpose**: Store individual items within purchase orders
- `po_item_id` (Text, Primary Key): Unique identifier
- `po_id` (Text, Foreign Key): Link to PURCHASE_ORDERS table
- `item_description` (Text, Required): Description of item
- `quantity` (Number, Required): Quantity ordered
- `unit` (Text): Unit of measurement
- `unit_price` (Number, Required): Price per unit
- `total_price` (Number, Calculated): quantity × unit_price
- `specifications` (Text): Item specifications

### 8. SUPPLIER_PAYMENTS
**Purpose**: Track payments to suppliers
- `supplier_payment_id` (Text, Primary Key): Unique identifier
- `supplier_id` (Text, Foreign Key): Link to SUPPLIERS table
- `po_id` (Text, Foreign Key): Link to PURCHASE_ORDERS table (optional)
- `amount` (Number, Required): Payment amount
- `payment_method` (Text): Cash/Bank Transfer/Check
- `currency` (Text): Payment currency
- `payment_date` (Date, Required): Date of payment
- `reference_number` (Text): Bank reference or check number
- `notes` (Text): Additional notes
- `created_date` (Timestamp): Record creation date

### 9. BLOCKS
**Purpose**: Track granite/stone blocks inventory
- `block_id` (Text, Primary Key): Unique identifier (e.g., BLK001)
- `supplier_id` (Text, Foreign Key): Link to SUPPLIERS table
- `po_id` (Text, Foreign Key): Link to PURCHASE_ORDERS table
- `block_type` (Text): Type of stone/granite
- `color` (Text): Block color
- `origin` (Text): Country/region of origin
- `length` (Number): Length in meters
- `width` (Number): Width in meters
- `height` (Number): Height in meters
- `volume` (Number, Calculated): length × width × height
- `cost_per_unit` (Number): Cost per cubic meter
- `total_cost` (Number, Calculated): volume × cost_per_unit
- `currency` (Text): Cost currency
- `received_date` (Date): Date received
- `location` (Text): Storage location
- `status` (Text): Available/In Use/Depleted
- `notes` (Text): Additional notes

### 10. CUTS
**Purpose**: Track cuts made from blocks for customers
- `cut_id` (Text, Primary Key): Unique identifier (e.g., CUT001)
- `block_id` (Text, Foreign Key): Link to BLOCKS table
- `customer_id` (Text, Foreign Key): Link to CUSTOMERS table
- `cut_type` (Text): Slab/Tile/Custom
- `length` (Number): Cut length in meters
- `width` (Number): Cut width in meters
- `thickness` (Number): Cut thickness in meters
- `area` (Number, Calculated): length × width
- `waste_percentage` (Number): Percentage of waste
- `cost_per_sqm` (Number): Cost per square meter
- `total_cost` (Number, Calculated): area × cost_per_sqm
- `currency` (Text): Cost currency
- `cut_date` (Date): Date of cutting
- `status` (Text): Planned/In Progress/Completed/Delivered
- `specifications` (Text): Cut specifications
- `notes` (Text): Additional notes

### 11. EXPENSES
**Purpose**: Track various business expenses
- `expense_id` (Text, Primary Key): Unique identifier
- `supplier_id` (Text, Foreign Key): Link to SUPPLIERS table (optional)
- `expense_category` (Text): Transportation/Maintenance/Utilities/Other
- `description` (Text, Required): Expense description
- `amount` (Number, Required): Expense amount
- `currency` (Text): Expense currency
- `expense_date` (Date, Required): Date of expense
- `payment_method` (Text): Cash/Bank Transfer/Check
- `reference_number` (Text): Reference number
- `notes` (Text): Additional notes
- `created_date` (Timestamp): Record creation date

### 12. USERS
**Purpose**: System user management
- `user_id` (Text, Primary Key): Unique identifier
- `username` (Text, Required, Unique): Login username
- `password_hash` (Text, Required): Encrypted password
- `full_name` (Text, Required): User's full name
- `email` (Text): User's email
- `role` (Text): Admin/Manager/Accountant/Operator
- `active_status` (Boolean): Active/Inactive status
- `last_login` (Timestamp): Last login time
- `created_date` (Timestamp): Account creation date
- `modified_date` (Timestamp): Last modification date

### 13. USER_SESSIONS
**Purpose**: Track user login sessions
- `session_id` (Text, Primary Key): Unique session identifier
- `user_id` (Text, Foreign Key): Link to USERS table
- `login_time` (Timestamp): Login time
- `logout_time` (Timestamp): Logout time
- `ip_address` (Text): User's IP address
- `active` (Boolean): Session active status

### 14. CUSTOMER_REPORTS
**Purpose**: Store generated customer reports
- `report_id` (Text, Primary Key): Unique identifier
- `customer_id` (Text, Foreign Key): Link to CUSTOMERS table
- `report_type` (Text): Account Statement/Sales Summary/Payment History
- `report_date` (Date): Report generation date
- `total_sales` (Number): Total sales amount
- `total_payments` (Number): Total payments received
- `outstanding_balance` (Number): Outstanding balance
- `report_data` (Text): JSON/XML report data
- `created_date` (Timestamp): Report creation date

### 15. SUPPLIER_REPORTS
**Purpose**: Store generated supplier reports
- `report_id` (Text, Primary Key): Unique identifier
- `supplier_id` (Text, Foreign Key): Link to SUPPLIERS table
- `report_type` (Text): Purchase Summary/Payment History/Cost Analysis
- `report_date` (Date): Report generation date
- `total_purchases` (Number): Total purchase amount
- `total_payments` (Number): Total payments made
- `outstanding_balance` (Number): Outstanding balance
- `report_data` (Text): JSON/XML report data
- `created_date` (Timestamp): Report creation date

## Key Relationships

1. **Customer → Invoices**: One-to-Many (One customer can have multiple invoices)
2. **Customer → Payments**: One-to-Many (One customer can make multiple payments)
3. **Customer → Cuts**: One-to-Many (One customer can order multiple cuts)
4. **Supplier → Purchase Orders**: One-to-Many (One supplier can have multiple POs)
5. **Supplier → Blocks**: One-to-Many (One supplier can supply multiple blocks)
6. **Invoice → Invoice Items**: One-to-Many (One invoice can have multiple items)
7. **Purchase Order → PO Items**: One-to-Many (One PO can have multiple items)
8. **Block → Cuts**: One-to-Many (One block can produce multiple cuts)
9. **Cut → Invoice Items**: One-to-One (One cut can be sold as one invoice item)

## Business Rules

1. **Customer Management**:
   - Customer ID must be unique and auto-generated
   - Phone number is required for all customers
   - Email is optional but recommended
   - Preferred payment method affects invoice terms

2. **Supplier Management**:
   - Supplier ID must be unique and auto-generated
   - Supplier type determines available products/services
   - Payment terms affect purchase order conditions

3. **Inventory Management**:
   - Block volume is automatically calculated
   - Cut area is automatically calculated
   - Waste percentage affects costing
   - Block status changes based on cuts made

4. **Financial Management**:
   - Invoice net amount = total - discount + tax
   - Outstanding balance = total invoices - total payments
   - Multi-currency support with conversion rates

5. **User Access Control**:
   - Admin: Full access to all modules
   - Manager: Access to all except user management
   - Accountant: Access to financial modules only
   - Operator: Access to inventory and basic customer data

## Next Steps

1. Implement FileMaker Pro database with these tables
2. Create relationships and validation rules
3. Design user interfaces for each module
4. Implement user access controls
5. Create reporting layouts
6. Develop Python integration scripts
