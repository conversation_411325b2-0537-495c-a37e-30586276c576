"""
Al-Hassan Stone Factory - Python Database Connector
Connects Python analytics to FileMaker Pro database
"""

import pyodbc
import pandas as pd
import json
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Any
import os
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    server: str
    database: str
    username: str
    password: str
    driver: str = "FileMaker ODBC"
    port: int = 2399

class FileMakerConnector:
    """
    Connector class for FileMaker Pro database integration
    Provides methods to read/write data and execute analytics
    """
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection = None
        self.connect()
    
    def connect(self) -> bool:
        """Establish connection to FileMaker Pro database"""
        try:
            connection_string = (
                f"DRIVER={{{self.config.driver}}};"
                f"SERVER={self.config.server};"
                f"PORT={self.config.port};"
                f"DATABASE={self.config.database};"
                f"UID={self.config.username};"
                f"PWD={self.config.password};"
            )
            
            self.connection = pyodbc.connect(connection_string)
            logger.info("Successfully connected to FileMaker Pro database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> pd.DataFrame:
        """Execute SQL query and return results as DataFrame"""
        try:
            if params:
                df = pd.read_sql(query, self.connection, params=params)
            else:
                df = pd.read_sql(query, self.connection)
            return df
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            return pd.DataFrame()
    
    def execute_non_query(self, query: str, params: Optional[tuple] = None) -> bool:
        """Execute non-query SQL statement (INSERT, UPDATE, DELETE)"""
        try:
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            self.connection.commit()
            cursor.close()
            return True
        except Exception as e:
            logger.error(f"Non-query execution failed: {str(e)}")
            return False

class CustomerAnalytics:
    """Customer analytics and intelligence functions"""
    
    def __init__(self, db_connector: FileMakerConnector):
        self.db = db_connector
    
    def get_customer_outstanding_balances(self) -> pd.DataFrame:
        """Get all customers with outstanding balances"""
        query = """
        SELECT 
            c.customer_id,
            c.full_name,
            c.phone_number,
            c.company_contractor_name,
            COALESCE(SUM(i.net_amount), 0) as total_invoices,
            COALESCE(SUM(p.amount), 0) as total_payments,
            (COALESCE(SUM(i.net_amount), 0) - COALESCE(SUM(p.amount), 0)) as outstanding_balance
        FROM CUSTOMERS c
        LEFT JOIN INVOICES i ON c.customer_id = i.customer_id
        LEFT JOIN PAYMENTS p ON c.customer_id = p.customer_id
        WHERE c.active_status = 1
        GROUP BY c.customer_id, c.full_name, c.phone_number, c.company_contractor_name
        HAVING (COALESCE(SUM(i.net_amount), 0) - COALESCE(SUM(p.amount), 0)) > 0
        ORDER BY outstanding_balance DESC
        """
        return self.db.execute_query(query)
    
    def get_overdue_customers(self, days_overdue: int = 30) -> pd.DataFrame:
        """Get customers with overdue payments"""
        cutoff_date = datetime.now() - timedelta(days=days_overdue)
        query = """
        SELECT 
            c.customer_id,
            c.full_name,
            c.phone_number,
            i.invoice_number,
            i.invoice_date,
            i.due_date,
            i.net_amount,
            COALESCE(SUM(p.amount), 0) as payments_received,
            (i.net_amount - COALESCE(SUM(p.amount), 0)) as amount_due,
            DATEDIFF(day, i.due_date, GETDATE()) as days_overdue
        FROM CUSTOMERS c
        INNER JOIN INVOICES i ON c.customer_id = i.customer_id
        LEFT JOIN PAYMENTS p ON i.invoice_id = p.invoice_id
        WHERE i.due_date < ? 
        AND i.status != 'Paid'
        AND c.active_status = 1
        GROUP BY c.customer_id, c.full_name, c.phone_number, 
                 i.invoice_number, i.invoice_date, i.due_date, i.net_amount
        HAVING (i.net_amount - COALESCE(SUM(p.amount), 0)) > 0
        ORDER BY days_overdue DESC, amount_due DESC
        """
        return self.db.execute_query(query, (cutoff_date,))
    
    def get_top_customers(self, limit: int = 10, period_months: int = 12) -> pd.DataFrame:
        """Get top customers by sales volume"""
        start_date = datetime.now() - timedelta(days=period_months * 30)
        query = """
        SELECT TOP (?)
            c.customer_id,
            c.full_name,
            c.company_contractor_name,
            COUNT(i.invoice_id) as total_invoices,
            SUM(i.net_amount) as total_sales,
            AVG(i.net_amount) as average_invoice,
            MAX(i.invoice_date) as last_purchase_date
        FROM CUSTOMERS c
        INNER JOIN INVOICES i ON c.customer_id = i.customer_id
        WHERE i.invoice_date >= ?
        AND c.active_status = 1
        GROUP BY c.customer_id, c.full_name, c.company_contractor_name
        ORDER BY total_sales DESC
        """
        return self.db.execute_query(query, (limit, start_date))
    
    def predict_customer_behavior(self, customer_id: str) -> Dict[str, Any]:
        """Predict customer future orders based on historical data"""
        # Get customer historical data
        query = """
        SELECT 
            i.invoice_date,
            i.net_amount,
            COUNT(ii.item_id) as items_count,
            SUM(ii.quantity) as total_quantity
        FROM INVOICES i
        INNER JOIN INVOICE_ITEMS ii ON i.invoice_id = ii.invoice_id
        WHERE i.customer_id = ?
        AND i.invoice_date >= DATEADD(year, -2, GETDATE())
        GROUP BY i.invoice_date, i.net_amount
        ORDER BY i.invoice_date
        """
        
        df = self.db.execute_query(query, (customer_id,))
        
        if df.empty:
            return {"prediction": "No historical data available"}
        
        # Simple prediction logic (can be enhanced with ML models)
        avg_order_value = df['net_amount'].mean()
        avg_order_frequency = len(df) / 24  # orders per month over 2 years
        last_order_date = df['invoice_date'].max()
        days_since_last_order = (datetime.now() - pd.to_datetime(last_order_date)).days
        
        # Predict next order
        expected_next_order_days = 30 / avg_order_frequency if avg_order_frequency > 0 else 90
        next_order_probability = max(0, 1 - (days_since_last_order / expected_next_order_days))
        
        return {
            "customer_id": customer_id,
            "avg_order_value": round(avg_order_value, 2),
            "avg_monthly_frequency": round(avg_order_frequency, 2),
            "days_since_last_order": days_since_last_order,
            "next_order_probability": round(next_order_probability, 2),
            "predicted_next_order_value": round(avg_order_value, 2),
            "recommendation": self._get_customer_recommendation(next_order_probability, days_since_last_order)
        }
    
    def _get_customer_recommendation(self, probability: float, days_since: int) -> str:
        """Generate customer engagement recommendation"""
        if probability > 0.7:
            return "High probability customer - prepare for next order"
        elif probability > 0.4:
            return "Medium probability - consider follow-up call"
        elif days_since > 90:
            return "Inactive customer - needs re-engagement campaign"
        else:
            return "Monitor customer activity"

class SupplierAnalytics:
    """Supplier analytics and performance tracking"""
    
    def __init__(self, db_connector: FileMakerConnector):
        self.db = db_connector
    
    def get_supplier_performance(self) -> pd.DataFrame:
        """Calculate supplier performance metrics"""
        query = """
        SELECT 
            s.supplier_id,
            s.supplier_name,
            s.supplier_type,
            COUNT(po.po_id) as total_orders,
            SUM(po.total_amount) as total_purchases,
            AVG(po.total_amount) as avg_order_value,
            COUNT(CASE WHEN po.status = 'Received' THEN 1 END) as completed_orders,
            (COUNT(CASE WHEN po.status = 'Received' THEN 1 END) * 100.0 / COUNT(po.po_id)) as completion_rate,
            AVG(DATEDIFF(day, po.order_date, po.expected_delivery)) as avg_delivery_time,
            MAX(po.order_date) as last_order_date
        FROM SUPPLIERS s
        LEFT JOIN PURCHASE_ORDERS po ON s.supplier_id = po.supplier_id
        WHERE s.active_status = 1
        AND po.order_date >= DATEADD(year, -1, GETDATE())
        GROUP BY s.supplier_id, s.supplier_name, s.supplier_type
        ORDER BY total_purchases DESC
        """
        return self.db.execute_query(query)
    
    def get_supplier_cost_analysis(self, period_months: int = 12) -> pd.DataFrame:
        """Analyze supplier costs and trends"""
        start_date = datetime.now() - timedelta(days=period_months * 30)
        query = """
        SELECT 
            s.supplier_id,
            s.supplier_name,
            s.supplier_type,
            YEAR(po.order_date) as year,
            MONTH(po.order_date) as month,
            SUM(po.total_amount) as monthly_cost,
            COUNT(po.po_id) as monthly_orders,
            AVG(po.total_amount) as avg_order_cost
        FROM SUPPLIERS s
        INNER JOIN PURCHASE_ORDERS po ON s.supplier_id = po.supplier_id
        WHERE po.order_date >= ?
        AND s.active_status = 1
        GROUP BY s.supplier_id, s.supplier_name, s.supplier_type, 
                 YEAR(po.order_date), MONTH(po.order_date)
        ORDER BY s.supplier_name, year, month
        """
        return self.db.execute_query(query, (start_date,))
    
    def identify_cost_savings_opportunities(self) -> List[Dict[str, Any]]:
        """Identify potential cost savings with suppliers"""
        # Get supplier comparison data
        query = """
        SELECT 
            s.supplier_type,
            s.supplier_id,
            s.supplier_name,
            AVG(poi.unit_price) as avg_unit_price,
            SUM(poi.total_price) as total_spent,
            COUNT(poi.po_item_id) as total_items
        FROM SUPPLIERS s
        INNER JOIN PURCHASE_ORDERS po ON s.supplier_id = po.supplier_id
        INNER JOIN PURCHASE_ORDER_ITEMS poi ON po.po_id = poi.po_id
        WHERE po.order_date >= DATEADD(month, -6, GETDATE())
        AND s.active_status = 1
        GROUP BY s.supplier_type, s.supplier_id, s.supplier_name
        HAVING COUNT(poi.po_item_id) >= 5
        ORDER BY s.supplier_type, avg_unit_price
        """
        
        df = self.db.execute_query(query)
        opportunities = []
        
        # Group by supplier type and find price variations
        for supplier_type in df['supplier_type'].unique():
            type_data = df[df['supplier_type'] == supplier_type]
            if len(type_data) > 1:
                min_price = type_data['avg_unit_price'].min()
                max_price = type_data['avg_unit_price'].max()
                
                if max_price > min_price * 1.2:  # 20% price difference threshold
                    high_cost_suppliers = type_data[type_data['avg_unit_price'] > min_price * 1.1]
                    
                    for _, supplier in high_cost_suppliers.iterrows():
                        potential_savings = (supplier['avg_unit_price'] - min_price) * supplier['total_items']
                        opportunities.append({
                            "supplier_type": supplier_type,
                            "supplier_name": supplier['supplier_name'],
                            "current_avg_price": supplier['avg_unit_price'],
                            "market_min_price": min_price,
                            "potential_savings": potential_savings,
                            "recommendation": f"Consider negotiating with {supplier['supplier_name']} or switching to lower-cost supplier"
                        })
        
        return sorted(opportunities, key=lambda x: x['potential_savings'], reverse=True)

class InventoryAnalytics:
    """Inventory and block utilization analytics"""
    
    def __init__(self, db_connector: FileMakerConnector):
        self.db = db_connector
    
    def get_block_utilization(self) -> pd.DataFrame:
        """Calculate block utilization rates"""
        query = """
        SELECT 
            b.block_id,
            b.block_type,
            b.color,
            b.volume as total_volume,
            COALESCE(SUM(c.area * c.thickness), 0) as used_volume,
            (b.volume - COALESCE(SUM(c.area * c.thickness), 0)) as remaining_volume,
            (COALESCE(SUM(c.area * c.thickness), 0) / b.volume * 100) as utilization_rate,
            b.status,
            b.location,
            DATEDIFF(day, b.received_date, GETDATE()) as days_in_inventory
        FROM BLOCKS b
        LEFT JOIN CUTS c ON b.block_id = c.block_id
        WHERE b.status IN ('Available', 'In Use')
        GROUP BY b.block_id, b.block_type, b.color, b.volume, b.status, b.location, b.received_date
        ORDER BY utilization_rate DESC
        """
        return self.db.execute_query(query)
    
    def get_slow_moving_inventory(self, days_threshold: int = 90) -> pd.DataFrame:
        """Identify slow-moving inventory items"""
        query = """
        SELECT 
            b.block_id,
            b.block_type,
            b.color,
            b.volume,
            b.location,
            b.received_date,
            DATEDIFF(day, b.received_date, GETDATE()) as days_in_inventory,
            COALESCE(COUNT(c.cut_id), 0) as cuts_made,
            b.total_cost,
            (b.total_cost / b.volume) as cost_per_cubic_meter
        FROM BLOCKS b
        LEFT JOIN CUTS c ON b.block_id = c.block_id
        WHERE b.status = 'Available'
        AND DATEDIFF(day, b.received_date, GETDATE()) > ?
        GROUP BY b.block_id, b.block_type, b.color, b.volume, b.location, 
                 b.received_date, b.total_cost
        HAVING COALESCE(COUNT(c.cut_id), 0) < 3
        ORDER BY days_in_inventory DESC
        """
        return self.db.execute_query(query, (days_threshold,))
    
    def predict_inventory_needs(self) -> Dict[str, Any]:
        """Predict future inventory requirements based on sales trends"""
        # Get historical cut data
        query = """
        SELECT 
            b.block_type,
            b.color,
            YEAR(c.cut_date) as year,
            MONTH(c.cut_date) as month,
            SUM(c.area * c.thickness) as volume_used,
            COUNT(c.cut_id) as cuts_count
        FROM BLOCKS b
        INNER JOIN CUTS c ON b.block_id = c.block_id
        WHERE c.cut_date >= DATEADD(year, -1, GETDATE())
        GROUP BY b.block_type, b.color, YEAR(c.cut_date), MONTH(c.cut_date)
        ORDER BY b.block_type, b.color, year, month
        """
        
        df = self.db.execute_query(query)
        
        if df.empty:
            return {"prediction": "No historical data available"}
        
        # Calculate monthly averages by block type and color
        monthly_avg = df.groupby(['block_type', 'color']).agg({
            'volume_used': 'mean',
            'cuts_count': 'mean'
        }).reset_index()
        
        # Get current inventory
        current_inventory_query = """
        SELECT 
            block_type,
            color,
            SUM(volume - COALESCE((SELECT SUM(area * thickness) 
                                   FROM CUTS WHERE block_id = BLOCKS.block_id), 0)) as available_volume
        FROM BLOCKS
        WHERE status IN ('Available', 'In Use')
        GROUP BY block_type, color
        """
        
        current_df = self.db.execute_query(current_inventory_query)
        
        # Merge and calculate recommendations
        recommendations = []
        for _, row in monthly_avg.iterrows():
            current_stock = current_df[
                (current_df['block_type'] == row['block_type']) & 
                (current_df['color'] == row['color'])
            ]
            
            available_volume = current_stock['available_volume'].iloc[0] if not current_stock.empty else 0
            monthly_usage = row['volume_used']
            months_of_stock = available_volume / monthly_usage if monthly_usage > 0 else float('inf')
            
            if months_of_stock < 2:  # Less than 2 months of stock
                recommendations.append({
                    "block_type": row['block_type'],
                    "color": row['color'],
                    "current_stock_months": round(months_of_stock, 1),
                    "monthly_usage": round(monthly_usage, 2),
                    "recommended_order": round(monthly_usage * 3, 2),  # 3 months supply
                    "priority": "High" if months_of_stock < 1 else "Medium"
                })
        
        return {
            "inventory_recommendations": recommendations,
            "total_recommendations": len(recommendations),
            "high_priority_items": len([r for r in recommendations if r['priority'] == 'High'])
        }

class AlertSystem:
    """Automated alert system for business intelligence"""

    def __init__(self, db_connector: FileMakerConnector):
        self.db = db_connector
        self.customer_analytics = CustomerAnalytics(db_connector)
        self.supplier_analytics = SupplierAnalytics(db_connector)
        self.inventory_analytics = InventoryAnalytics(db_connector)

    def generate_daily_alerts(self) -> List[Dict[str, Any]]:
        """Generate daily business alerts"""
        alerts = []

        # Overdue payment alerts
        overdue_customers = self.customer_analytics.get_overdue_customers(30)
        if not overdue_customers.empty:
            total_overdue = overdue_customers['amount_due'].sum()
            alerts.append({
                "type": "overdue_payments",
                "severity": "high",
                "message": f"{len(overdue_customers)} customers have overdue payments totaling {total_overdue:,.2f}",
                "data": overdue_customers.to_dict('records')
            })

        # Low inventory alerts
        inventory_predictions = self.inventory_analytics.predict_inventory_needs()
        if inventory_predictions.get('high_priority_items', 0) > 0:
            alerts.append({
                "type": "low_inventory",
                "severity": "medium",
                "message": f"{inventory_predictions['high_priority_items']} items need urgent restocking",
                "data": inventory_predictions['inventory_recommendations']
            })

        # Slow-moving inventory alerts
        slow_inventory = self.inventory_analytics.get_slow_moving_inventory(120)
        if not slow_inventory.empty:
            total_value = slow_inventory['total_cost'].sum()
            alerts.append({
                "type": "slow_inventory",
                "severity": "low",
                "message": f"{len(slow_inventory)} blocks have been in inventory for over 120 days (Value: {total_value:,.2f})",
                "data": slow_inventory.to_dict('records')
            })

        return alerts

    def send_alert_to_filemaker(self, alert: Dict[str, Any]) -> bool:
        """Send alert back to FileMaker Pro for display"""
        try:
            # Insert alert into a FileMaker alerts table
            query = """
            INSERT INTO ALERTS (alert_type, severity, message, alert_data, created_date)
            VALUES (?, ?, ?, ?, ?)
            """

            return self.db.execute_non_query(
                query,
                (alert['type'], alert['severity'], alert['message'],
                 json.dumps(alert['data']), datetime.now())
            )
        except Exception as e:
            logger.error(f"Failed to send alert to FileMaker: {str(e)}")
            return False


# Example usage and configuration
def create_database_connection() -> FileMakerConnector:
    """Create and return database connection"""
    config = DatabaseConfig(
        server="localhost",  # FileMaker Server address
        database="AlHassanStone.fmp12",
        username="admin",
        password="your_password_here",
        driver="FileMaker ODBC"
    )

    return FileMakerConnector(config)


def run_daily_analytics():
    """Run daily analytics and generate alerts"""
    db = create_database_connection()

    try:
        # Initialize analytics modules
        customer_analytics = CustomerAnalytics(db)
        supplier_analytics = SupplierAnalytics(db)
        inventory_analytics = InventoryAnalytics(db)
        alert_system = AlertSystem(db)

        # Generate and process alerts
        alerts = alert_system.generate_daily_alerts()

        for alert in alerts:
            alert_system.send_alert_to_filemaker(alert)
            logger.info(f"Generated alert: {alert['type']} - {alert['message']}")

        logger.info(f"Daily analytics completed. Generated {len(alerts)} alerts.")

    except Exception as e:
        logger.error(f"Daily analytics failed: {str(e)}")

    finally:
        db.disconnect()


if __name__ == "__main__":
    run_daily_analytics()
