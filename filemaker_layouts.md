# Al-Hassan Stone Factory - FileMaker Pro Layout Designs

## Main Navigation Layout

### Dashboard Layout (Main Menu)
**Layout Name**: Dashboard
**Table**: USERS (for user context)

**Elements**:
- Header with company logo and user info
- Main navigation buttons:
  - Customer Management
  - Supplier Management
  - Inventory Management
  - Sales & Invoicing
  - Purchase Orders
  - Financial Reports
  - System Administration
- Quick stats widgets:
  - Total Active Customers
  - Total Active Suppliers
  - Available Blocks
  - Pending Invoices
  - Outstanding Receivables
  - Outstanding Payables

**Scripts**:
- Navigate to Customer List
- Navigate to Supplier List
- Navigate to Inventory
- Navigate to Sales
- Navigate to Reports
- Logout

## Customer Management Layouts

### Customer List Layout
**Layout Name**: Customer_List
**Table**: CUSTOMERS

**Elements**:
- Search field (global field for filtering)
- List view with columns:
  - Customer ID
  - Full Name
  - Phone Number
  - Company Name
  - Outstanding Balance
  - Last Transaction Date
- Action buttons:
  - New Customer
  - Edit Customer
  - View Details
  - Generate Report
  - Delete Customer (with confirmation)

**Portal**: Recent transactions (from INVOICES table)

### Customer Detail Layout
**Layout Name**: Customer_Detail
**Table**: CUSTOMERS

**Elements**:
- Customer information form:
  - Customer ID (auto-generated, not editable)
  - Full Name (required)
  - Phone Number (required)
  - Address
  - Email
  - Company/Contractor Name
  - Preferred Payment Method (dropdown)
  - Currency (dropdown)
  - Notes (text area)
  - Active Status (checkbox)
  - Created Date (display only)
  - Modified Date (display only)

**Tabs**:
1. **Basic Info**: Customer details form
2. **Invoices**: Portal showing all customer invoices
3. **Payments**: Portal showing all customer payments
4. **Cuts**: Portal showing all cuts for this customer
5. **Reports**: Generated reports for this customer

**Action Buttons**:
- Save
- Cancel
- New Invoice
- Record Payment
- Generate Statement
- Print Customer Card

### Customer Invoice Layout
**Layout Name**: Customer_Invoice
**Table**: INVOICES

**Elements**:
- Invoice header:
  - Invoice Number (auto-generated)
  - Customer (dropdown with type-ahead)
  - Invoice Date
  - Due Date
  - Currency
  - Status
- Invoice items portal (INVOICE_ITEMS):
  - Product Description
  - Quantity
  - Unit
  - Unit Price
  - Total Price
  - Cut ID (if applicable)
- Invoice totals:
  - Subtotal
  - Discount Amount
  - Tax Amount
  - Net Amount
- Notes field

**Action Buttons**:
- Add Item
- Remove Item
- Calculate Totals
- Save Invoice
- Print Invoice
- Send Invoice
- Record Payment

## Supplier Management Layouts

### Supplier List Layout
**Layout Name**: Supplier_List
**Table**: SUPPLIERS

**Elements**:
- Search field for filtering
- List view with columns:
  - Supplier ID
  - Supplier Name
  - Supplier Type
  - Contact Person
  - Phone Number
  - Outstanding Balance
  - Last Purchase Date
- Action buttons:
  - New Supplier
  - Edit Supplier
  - View Details
  - Generate Report
  - Delete Supplier

### Supplier Detail Layout
**Layout Name**: Supplier_Detail
**Table**: SUPPLIERS

**Elements**:
- Supplier information form:
  - Supplier ID (auto-generated)
  - Supplier Name (required)
  - Supplier Type (dropdown)
  - Contact Person
  - Phone Number (required)
  - Email
  - Address
  - Payment Terms
  - Products/Services
  - Active Status
  - Created/Modified dates

**Tabs**:
1. **Basic Info**: Supplier details
2. **Purchase Orders**: Portal showing all POs
3. **Payments**: Portal showing all payments to supplier
4. **Blocks**: Portal showing all blocks from supplier
5. **Expenses**: Portal showing supplier-related expenses

**Action Buttons**:
- Save
- Cancel
- New Purchase Order
- Record Payment
- Generate Report

## Inventory Management Layouts

### Block List Layout
**Layout Name**: Block_List
**Table**: BLOCKS

**Elements**:
- Search and filter fields:
  - Block Type
  - Color
  - Status
  - Supplier
- List view with columns:
  - Block ID
  - Block Type
  - Color
  - Dimensions (L×W×H)
  - Volume
  - Supplier
  - Status
  - Location
  - Remaining Volume
- Action buttons:
  - New Block
  - Edit Block
  - Create Cut
  - View Details
  - Update Status

### Block Detail Layout
**Layout Name**: Block_Detail
**Table**: BLOCKS

**Elements**:
- Block information form:
  - Block ID (auto-generated)
  - Supplier (dropdown)
  - Purchase Order (dropdown)
  - Block Type
  - Color
  - Origin
  - Dimensions (Length, Width, Height)
  - Volume (calculated)
  - Cost per Unit
  - Total Cost (calculated)
  - Currency
  - Received Date
  - Location
  - Status
  - Notes

**Portal**: Cuts made from this block (CUTS table)

**Action Buttons**:
- Save
- Cancel
- Create Cut
- Update Location
- Change Status

### Cut Management Layout
**Layout Name**: Cut_Management
**Table**: CUTS

**Elements**:
- Cut information form:
  - Cut ID (auto-generated)
  - Block (dropdown with available blocks)
  - Customer (dropdown)
  - Cut Type
  - Dimensions (Length, Width, Thickness)
  - Area (calculated)
  - Waste Percentage
  - Cost per SQM
  - Total Cost (calculated)
  - Currency
  - Cut Date
  - Status
  - Specifications
  - Notes

**Action Buttons**:
- Save
- Cancel
- Add to Invoice
- Update Status
- Print Cut Sheet

## Purchase Order Layouts

### Purchase Order List Layout
**Layout Name**: PO_List
**Table**: PURCHASE_ORDERS

**Elements**:
- Search and filter fields
- List view with columns:
  - PO Number
  - Supplier
  - Order Date
  - Total Amount
  - Status
  - Expected Delivery
- Action buttons:
  - New PO
  - Edit PO
  - View Details
  - Receive Items
  - Cancel PO

### Purchase Order Detail Layout
**Layout Name**: PO_Detail
**Table**: PURCHASE_ORDERS

**Elements**:
- PO header:
  - PO Number (auto-generated)
  - Supplier (dropdown)
  - Order Date
  - Expected Delivery
  - Status
  - Currency
  - Notes
- PO items portal (PURCHASE_ORDER_ITEMS):
  - Item Description
  - Quantity
  - Unit
  - Unit Price
  - Total Price
  - Specifications
- PO totals:
  - Total Amount

**Action Buttons**:
- Add Item
- Remove Item
- Save PO
- Print PO
- Send to Supplier
- Receive Items
- Record Payment

## Financial Management Layouts

### Payment Entry Layout
**Layout Name**: Payment_Entry
**Table**: PAYMENTS

**Elements**:
- Payment form:
  - Payment ID (auto-generated)
  - Customer (dropdown)
  - Invoice (dropdown, filtered by customer)
  - Amount
  - Payment Method (dropdown)
  - Currency
  - Payment Date
  - Reference Number
  - Notes

**Action Buttons**:
- Save Payment
- Cancel
- Print Receipt
- Apply to Multiple Invoices

### Supplier Payment Layout
**Layout Name**: Supplier_Payment
**Table**: SUPPLIER_PAYMENTS

**Elements**:
- Payment form:
  - Payment ID (auto-generated)
  - Supplier (dropdown)
  - Purchase Order (dropdown, filtered by supplier)
  - Amount
  - Payment Method (dropdown)
  - Currency
  - Payment Date
  - Reference Number
  - Notes

**Action Buttons**:
- Save Payment
- Cancel
- Print Voucher

## Reporting Layouts

### Customer Statement Layout
**Layout Name**: Customer_Statement
**Table**: CUSTOMERS

**Elements**:
- Customer header information
- Date range selection
- Statement details:
  - Opening balance
  - Invoices (with dates and amounts)
  - Payments (with dates and amounts)
  - Closing balance
- Summary totals

### Supplier Report Layout
**Layout Name**: Supplier_Report
**Table**: SUPPLIERS

**Elements**:
- Supplier header information
- Date range selection
- Report details:
  - Purchase orders
  - Payments made
  - Outstanding balance
- Summary totals

### Sales Report Layout
**Layout Name**: Sales_Report
**Table**: INVOICES

**Elements**:
- Date range selection
- Customer filter
- Sales summary:
  - Total sales by customer
  - Total sales by period
  - Top customers
  - Payment status summary

### Inventory Report Layout
**Layout Name**: Inventory_Report
**Table**: BLOCKS

**Elements**:
- Inventory summary:
  - Available blocks by type
  - Block locations
  - Low stock alerts
  - Utilization rates

## User Administration Layouts

### User Management Layout
**Layout Name**: User_Management
**Table**: USERS

**Elements**:
- User list with columns:
  - Username
  - Full Name
  - Role
  - Active Status
  - Last Login
- User detail form:
  - Username
  - Full Name
  - Email
  - Role (dropdown)
  - Active Status
  - Password change option

**Action Buttons**:
- New User
- Edit User
- Reset Password
- Deactivate User
- View Login History

### Login Layout
**Layout Name**: Login
**Table**: USERS

**Elements**:
- Company logo
- Login form:
  - Username field
  - Password field
  - Remember me checkbox
- Login button
- Forgot password link

**Scripts**:
- Authenticate User
- Create Session
- Navigate to Dashboard

## Layout Design Standards

### Color Scheme
- Primary: #2C3E50 (Dark Blue-Gray)
- Secondary: #3498DB (Blue)
- Accent: #E74C3C (Red for alerts)
- Success: #27AE60 (Green)
- Background: #ECF0F1 (Light Gray)
- Text: #2C3E50 (Dark)

### Typography
- Headers: Arial Bold, 14pt
- Labels: Arial, 10pt
- Data: Arial, 9pt
- Buttons: Arial Bold, 10pt

### Button Standards
- Primary buttons: Blue background, white text
- Secondary buttons: Gray background, dark text
- Danger buttons: Red background, white text
- Size: 100px width, 25px height minimum

### Field Standards
- Text fields: White background, gray border
- Dropdowns: White background with arrow indicator
- Required fields: Light yellow background
- Read-only fields: Light gray background

### Portal Standards
- Alternating row colors (white/light gray)
- Header row with dark background
- Scrollable with scroll bar
- Minimum 5 rows visible

### Navigation Standards
- Breadcrumb navigation at top
- Consistent button placement
- Tab order for keyboard navigation
- Tooltips for complex fields

## Responsive Design Considerations

### Desktop (1024px and above)
- Full layout with all elements visible
- Side-by-side forms and portals
- Multiple columns in list views

### Tablet (768px - 1023px)
- Stacked layout for forms
- Reduced columns in list views
- Touch-friendly button sizes

### Mobile (below 768px)
- Single column layout
- Simplified navigation
- Essential fields only
- Large touch targets

## Accessibility Features

- High contrast mode support
- Keyboard navigation
- Screen reader compatibility
- Font size adjustment
- Color-blind friendly design
